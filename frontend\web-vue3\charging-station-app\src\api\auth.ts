import request from '@/utils/request'

// 登录
export function login(username: string, password: string) {
  return request({
    url: '/api/auth/login',
    method: 'post',
    data: {
      username,
      password
    }
  })
}

// 退出登录
export function logout() {
  return request({
    url: '/api/auth/logout',
    method: 'post'
  })
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: '/api/auth/me',
    method: 'get'
  })
}

// 注册
export function register(data: any) {
  return request({
    url: '/api/auth/register',
    method: 'post',
    data
  })
}

// 修改密码
export function changePassword(oldPassword: string, newPassword: string) {
  return request({
    url: '/api/auth/change-password',
    method: 'post',
    data: {
      oldPassword,
      newPassword
    }
  })
} 