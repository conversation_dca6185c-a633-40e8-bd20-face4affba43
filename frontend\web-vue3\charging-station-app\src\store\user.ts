import { defineStore } from 'pinia'
import { login, logout, getUserInfo } from '@/api/auth'
import { setToken, removeToken, getToken } from '@/utils/auth'

interface UserState {
  token: string
  userId: string
  username: string
  avatar: string
  roles: string[]
  permissions: string[]
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    token: getToken() || '',
    userId: '',
    username: '',
    avatar: '',
    roles: [],
    permissions: []
  }),
  
  getters: {
    isLoggedIn: (state) => !!state.token,
    hasRole: (state) => (role: string) => state.roles.includes(role),
    hasPermission: (state) => (permission: string) => state.permissions.includes(permission)
  },
  
  actions: {
    // 登录
    async login(username: string, password: string) {
      try {
        const response = await login(username, password)
        const { token } = response.data
        
        this.token = token
        setToken(token)
        return Promise.resolve()
      } catch (error) {
        return Promise.reject(error)
      }
    },
    
    // 获取用户信息
    async getInfo() {
      try {
        const response = await getUserInfo()
        const { userId, username, avatar, roles, permissions } = response.data
        
        this.userId = userId
        this.username = username
        this.avatar = avatar || ''
        this.roles = roles || []
        this.permissions = permissions || []
        
        return Promise.resolve(response.data)
      } catch (error) {
        return Promise.reject(error)
      }
    },
    
    // 退出登录
    async logout() {
      try {
        if (this.token) {
          await logout()
        }
      } catch (error) {
        console.error('Logout failed:', error)
      } finally {
        this.resetState()
        removeToken()
      }
    },
    
    // 重置状态
    resetState() {
      this.token = ''
      this.userId = ''
      this.username = ''
      this.avatar = ''
      this.roles = []
      this.permissions = []
    }
  }
}) 