package com.sic.tbox;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Bean;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;

@SpringBootApplication
@EnableDiscoveryClient
@EnableWebSocketMessageBroker
public class TboxServiceApplication implements WebSocketMessageBrokerConfigurer {
    
    public static void main(String[] args) {
        SpringApplication.run(TboxServiceApplication.class, args);
    }
    
    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        registry.addEndpoint("/tbox-websocket")
                .setAllowedOrigins("*")
                .withSockJS();
    }
} 