package com.sic.charging.service;

import com.sic.charging.model.ChargingPile;

import java.util.List;
import java.util.Optional;

public interface ChargingPileService {
    
    ChargingPile createPile(ChargingPile pile);
    
    Optional<ChargingPile> getPileById(Long id);
    
    Optional<ChargingPile> getPileByCode(String pileCode);
    
    List<ChargingPile> getAllPiles();
    
    List<ChargingPile> getPilesByStationId(Long stationId);
    
    List<ChargingPile> getAvailablePilesByStationId(Long stationId);
    
    ChargingPile updatePile(Long id, ChargingPile pile);
    
    void deletePile(Long id);
    
    void updatePileStatus(Long pileId, String status);
} 