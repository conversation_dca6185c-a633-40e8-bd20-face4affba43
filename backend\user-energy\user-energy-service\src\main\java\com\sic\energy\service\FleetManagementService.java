package com.sic.energy.service;

import com.sic.energy.model.FleetVehicle;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface FleetManagementService {
    
    List<FleetVehicle> getFleetVehicles(String fleetId);
    
    FleetVehicle saveVehicle(FleetVehicle vehicle);
    
    Optional<FleetVehicle> getVehicleByVin(String vin);
    
    Optional<FleetVehicle> updateVehicleStatus(String vin, String status);
    
    Map<String, Object> getFleetAnalytics(String fleetId);
    
    List<Map<String, Object>> getMaintenanceSchedule(String fleetId);
} 