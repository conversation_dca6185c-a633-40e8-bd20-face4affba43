# 架构设计说明

## 一、总体架构

系统采用“云-边-端”三层分布式架构：
- 端：车载HMI、APP、Web、小程序等多端接入
- 边：边缘计算节点（如Jetson AGX）
- 云：微服务集群，统一API网关，分布式数据库与中间件

## 二、主要技术选型
- 前端：Qt 6、Flutter、React、Vue3、Uni-app
- 后端：Spring Boot、Go+Gin、Python+FastAPI、Node.js+Express、C++/ROS2
- 数据库：MySQL、PostgreSQL、InfluxDB、MongoDB、Redis
- 中间件：RabbitMQ、Kafka、Kubernetes、Prometheus、Grafana、ELK
- 区块链：Hyperledger Fabric
- 通信协议：RESTful API、gRPC、MQTT、WebSocket、CAN、LTE-V2X、5G

## 三、模块分布
- 详见顶层README和各模块README

## 四、部署与运维
- 容器化部署，K8s编排，支持弹性伸缩与灰度发布
- 监控、日志、告警、备份等基础设施完善 