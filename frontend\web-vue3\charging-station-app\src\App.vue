<template>
  <div class="app-container">
    <el-config-provider :locale="zhCn">
      <el-container>
        <el-header height="60px">
          <app-header />
        </el-header>
        <el-container>
          <el-aside width="200px">
            <app-sidebar />
          </el-aside>
          <el-main>
            <router-view />
          </el-main>
        </el-container>
      </el-container>
    </el-config-provider>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import zhCn from 'element-plus/lib/locale/lang/zh-cn'
import AppHeader from '@/components/layout/AppHeader.vue'
import AppSidebar from '@/components/layout/AppSidebar.vue'

export default defineComponent({
  name: 'App',
  components: {
    AppHeader,
    AppSidebar
  },
  setup() {
    return {
      zhCn
    }
  }
})
</script>

<style lang="scss">
.app-container {
  height: 100%;
  
  .el-container {
    height: 100%;
  }
  
  .el-header {
    padding: 0;
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 100;
  }
  
  .el-aside {
    background-color: #304156;
    transition: width 0.3s;
    overflow-x: hidden;
  }
  
  .el-main {
    padding: 20px;
    background-color: #f5f7fa;
    overflow-y: auto;
  }
}
</style> 