{"name": "battery-trace-network", "version": "1.0.0", "client": {"organization": "Org1", "connection": {"timeout": {"peer": {"endorser": "300"}, "orderer": "300"}}}, "organizations": {"Org1": {"mspid": "Org1MSP", "peers": ["peer0.org1.example.com"], "certificateAuthorities": ["ca.org1.example.com"]}}, "peers": {"peer0.org1.example.com": {"url": "grpcs://localhost:7051", "tlsCACerts": {"path": "crypto-config/peerOrganizations/org1.example.com/peers/peer0.org1.example.com/tls/ca.crt"}, "grpcOptions": {"ssl-target-name-override": "peer0.org1.example.com", "hostnameOverride": "peer0.org1.example.com"}}}, "certificateAuthorities": {"ca.org1.example.com": {"url": "https://localhost:7054", "caName": "ca-org1", "tlsCACerts": {"path": "crypto-config/peerOrganizations/org1.example.com/ca/ca.org1.example.com-cert.pem"}, "httpOptions": {"verify": false}}}, "channels": {"battery-channel": {"orderers": ["orderer.example.com"], "peers": {"peer0.org1.example.com": {"endorsingPeer": true, "chaincodeQuery": true, "ledgerQuery": true, "eventSource": true}}}}, "orderers": {"orderer.example.com": {"url": "grpcs://localhost:7050", "tlsCACerts": {"path": "crypto-config/ordererOrganizations/example.com/orderers/orderer.example.com/tls/ca.crt"}, "grpcOptions": {"ssl-target-name-override": "orderer.example.com", "hostnameOverride": "orderer.example.com"}}}}