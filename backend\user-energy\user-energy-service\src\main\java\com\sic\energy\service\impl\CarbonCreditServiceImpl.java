package com.sic.energy.service.impl;

import com.sic.energy.model.CarbonCredit;
import com.sic.energy.repository.CarbonCreditRepository;
import com.sic.energy.service.CarbonCreditService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CarbonCreditServiceImpl implements CarbonCreditService {

    @Autowired
    private CarbonCreditRepository carbonCreditRepository;

    @Override
    public List<CarbonCredit> getUserCarbonCredits(String userId) {
        return carbonCreditRepository.findByUserId(userId);
    }

    @Override
    public CarbonCredit addCarbonCredits(CarbonCredit carbonCredit) {
        if (carbonCredit.getTimestamp() == null) {
            carbonCredit.setTimestamp(LocalDateTime.now());
        }
        return carbonCreditRepository.save(carbonCredit);
    }

    @Override
    public Double getTotalCarbonCredits(String userId) {
        List<CarbonCredit> credits = carbonCreditRepository.findByUserId(userId);
        return credits.stream().mapToDouble(CarbonCredit::getCredits).sum();
    }

    @Override
    public Map<String, Object> getUserCarbonCreditsSummary(String userId) {
        List<CarbonCredit> credits = carbonCreditRepository.findByUserId(userId);
        
        Map<String, Object> summary = new HashMap<>();
        summary.put("totalCredits", credits.stream().mapToDouble(CarbonCredit::getCredits).sum());
        
        // 按活动类型统计
        Map<String, Double> byActivityType = credits.stream()
                .collect(Collectors.groupingBy(
                        CarbonCredit::getActivityType,
                        Collectors.summingDouble(CarbonCredit::getCredits)
                ));
        summary.put("byActivityType", byActivityType);
        
        // 最近10条记录
        List<CarbonCredit> recentCredits = credits.stream()
                .sorted(Comparator.comparing(CarbonCredit::getTimestamp).reversed())
                .limit(10)
                .collect(Collectors.toList());
        summary.put("recentActivity", recentCredits);
        
        return summary;
    }

    @Override
    public boolean redeemCredits(String userId, Double creditsToRedeem, String purpose) {
        double totalCredits = getTotalCarbonCredits(userId);
        
        // 检查是否有足够的积分
        if (totalCredits < creditsToRedeem) {
            return false;
        }
        
        // 创建一个负的积分记录表示兑换
        CarbonCredit redemption = new CarbonCredit();
        redemption.setUserId(userId);
        redemption.setCredits(-creditsToRedeem); // 使用负值表示兑换
        redemption.setActivityType("redemption");
        redemption.setDescription("积分兑换: " + purpose);
        redemption.setTimestamp(LocalDateTime.now());
        
        carbonCreditRepository.save(redemption);
        return true;
    }
} 