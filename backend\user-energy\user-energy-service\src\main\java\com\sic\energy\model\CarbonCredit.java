package com.sic.energy.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import java.time.LocalDateTime;

@Data
@Document(collection = "carbon_credits")
public class CarbonCredit {
    @Id
    private String id;
    
    private String userId;
    private Double credits;
    private String activityType;
    private String description;
    private LocalDateTime timestamp;
} 