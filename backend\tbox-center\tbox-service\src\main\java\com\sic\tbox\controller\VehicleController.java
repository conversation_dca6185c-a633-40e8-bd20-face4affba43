package com.sic.tbox.controller;

import com.sic.tbox.model.Vehicle;
import com.sic.tbox.service.VehicleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

@RestController
@RequestMapping("/api/tbox/vehicles")
public class VehicleController {
    
    @Autowired
    private VehicleService vehicleService;
    
    @GetMapping("/{vin}")
    public ResponseEntity<Vehicle> getVehicleByVin(@PathVariable String vin) {
        return vehicleService.getVehicleByVin(vin)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @GetMapping
    public List<Vehicle> getAllVehicles() {
        return vehicleService.getAllVehicles();
    }
    
    @PostMapping("/command")
    public ResponseEntity<?> sendCommand(@RequestParam String vin, @RequestParam String command) {
        boolean success = vehicleService.sendCommand(vin, command);
        Map<String, String> response = new HashMap<>();
        
        if (success) {
            response.put("message", "命令已发送");
            return ResponseEntity.ok(response);
        } else {
            response.put("message", "命令发送失败");
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    @GetMapping("/status/{vin}")
    public ResponseEntity<?> getVehicleStatus(@PathVariable String vin) {
        return vehicleService.getVehicleStatus(vin)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
} 