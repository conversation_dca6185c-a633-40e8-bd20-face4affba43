package com.sic.energy.controller;

import com.sic.energy.model.CarbonCredit;
import com.sic.energy.service.CarbonCreditService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

@RestController
@RequestMapping("/api/user/carbon-credits")
public class CarbonCreditController {

    @Autowired
    private CarbonCreditService carbonCreditService;
    
    @GetMapping("/{userId}")
    public ResponseEntity<List<CarbonCredit>> getUserCarbonCredits(@PathVariable String userId) {
        return ResponseEntity.ok(carbonCreditService.getUserCarbonCredits(userId));
    }
    
    @PostMapping
    public ResponseEntity<CarbonCredit> addCarbonCredits(@RequestBody CarbonCredit carbonCredit) {
        return ResponseEntity.ok(carbonCreditService.addCarbonCredits(carbonCredit));
    }
    
    @GetMapping("/{userId}/total")
    public ResponseEntity<Double> getTotalCarbonCredits(@PathVariable String userId) {
        return ResponseEntity.ok(carbonCreditService.getTotalCarbonCredits(userId));
    }
    
    @GetMapping("/{userId}/summary")
    public ResponseEntity<Map<String, Object>> getCarbonCreditsSummary(@PathVariable String userId) {
        Map<String, Object> summary = carbonCreditService.getUserCarbonCreditsSummary(userId);
        return ResponseEntity.ok(summary);
    }
    
    @PostMapping("/redeem")
    public ResponseEntity<Map<String, Object>> redeemCredits(
            @RequestParam String userId, 
            @RequestParam Double credits, 
            @RequestParam String purpose) {
        
        boolean success = carbonCreditService.redeemCredits(userId, credits, purpose);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("status", "success");
            response.put("message", "积分兑换成功");
            response.put("remainingCredits", carbonCreditService.getTotalCarbonCredits(userId));
        } else {
            response.put("status", "failed");
            response.put("message", "积分兑换失败，请检查积分余额");
        }
        
        return ResponseEntity.ok(response);
    }
} 