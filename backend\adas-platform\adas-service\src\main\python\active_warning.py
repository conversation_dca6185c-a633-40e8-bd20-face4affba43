import logging
import math
import numpy as np
from enum import Enum

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WarningLevel(Enum):
    NONE = 0
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

class ActiveWarning:
    def __init__(self):
        # 碰撞预警参数
        self.collision_warning_thresholds = {
            WarningLevel.LOW: 20.0,     # 米
            WarningLevel.MEDIUM: 10.0,  # 米
            WarningLevel.HIGH: 5.0,     # 米
            WarningLevel.CRITICAL: 2.0  # 米
        }
        
        # 车道偏离参数
        self.lane_departure_thresholds = {
            WarningLevel.LOW: 0.3,      # 米
            WarningLevel.MEDIUM: 0.5,   # 米
            WarningLevel.HIGH: 0.8      # 米
        }
        
        # 驾驶员注意力参数
        self.driver_attention_threshold = 2.0  # 秒
        
        logger.info("初始化主动预警模块")
        
    def check_forward_collision(self, distance_to_object, relative_speed=None):
        """
        前向碰撞预警
        
        参数:
            distance_to_object: 前方物体距离（米）
            relative_speed: 相对速度（米/秒，可选）
        
        返回:
            warning_data: 预警数据字典
        """
        # 初始化无预警
        warning_level = WarningLevel.NONE
        
        # 根据距离确定预警级别
        for level, threshold in self.collision_warning_thresholds.items():
            if distance_to_object < threshold:
                warning_level = level
                break
        
        # 如果提供了相对速度，调整预警级别
        if relative_speed is not None and relative_speed > 0:  # 正值表示接近
            # 计算碰撞时间 (TTC = 距离/相对速度)
            ttc = distance_to_object / relative_speed
            
            # 根据TTC调整预警级别
            if ttc < 1.0:  # 1秒内碰撞
                warning_level = WarningLevel.CRITICAL
            elif ttc < 2.0 and warning_level.value < WarningLevel.HIGH.value:
                warning_level = WarningLevel.HIGH
            elif ttc < 3.0 and warning_level.value < WarningLevel.MEDIUM.value:
                warning_level = WarningLevel.MEDIUM
        
        # 只有在有预警时才返回数据
        if warning_level != WarningLevel.NONE:
            warning_data = {
                "warning_type": "FORWARD_COLLISION",
                "level": warning_level.name,
                "level_value": warning_level.value,
                "distance": distance_to_object
            }
            
            if relative_speed is not None:
                warning_data["relative_speed"] = relative_speed
                warning_data["time_to_collision"] = distance_to_object / relative_speed if relative_speed > 0 else float('inf')
                
            return warning_data
            
        return None
        
    def check_lane_departure(self, lane_position, steering_angle=None):
        """
        车道偏离预警
        
        参数:
            lane_position: 距离车道中心线的距离（米，左侧为负，右侧为正）
            steering_angle: 方向盘转向角度（度，可选）
        
        返回:
            warning_data: 预警数据字典
        """
        # 转换为绝对偏离距离
        deviation = abs(lane_position)
        warning_level = WarningLevel.NONE
        
        # 根据偏离距离确定预警级别
        for level, threshold in self.lane_departure_thresholds.items():
            if deviation > threshold:
                warning_level = level
        
        # 如果提供了方向盘转角，判断是否正在修正偏离
        is_correcting = False
        if steering_angle is not None:
            # 如果方向盘转向与偏离方向相反，可能正在修正
            if (lane_position < 0 and steering_angle > 5) or (lane_position > 0 and steering_angle < -5):
                is_correcting = True
        
        # 如果驾驶员正在修正，降低预警级别
        if is_correcting and warning_level.value > WarningLevel.LOW.value:
            warning_level = WarningLevel(warning_level.value - 1)
        
        # 只有在有预警时才返回数据
        if warning_level != WarningLevel.NONE:
            return {
                "warning_type": "LANE_DEPARTURE",
                "level": warning_level.name,
                "level_value": warning_level.value,
                "direction": "LEFT" if lane_position < 0 else "RIGHT",
                "deviation": deviation,
                "is_correcting": is_correcting
            }
        
        return None
    
    def check_driver_attention(self, eye_closed_time, head_position_deviation):
        """
        驾驶员注意力预警
        
        参数:
            eye_closed_time: 眼睛闭合时间（秒）
            head_position_deviation: 头部位置偏离（归一化值，0-1）
        
        返回:
            warning_data: 预警数据字典
        """
        warning_level = WarningLevel.NONE
        
        # 根据眼睛闭合时间判断
        if eye_closed_time > self.driver_attention_threshold:
            warning_level = WarningLevel.CRITICAL
        elif eye_closed_time > self.driver_attention_threshold * 0.7:
            warning_level = WarningLevel.HIGH
        elif eye_closed_time > self.driver_attention_threshold * 0.5:
            warning_level = WarningLevel.MEDIUM
        
        # 考虑头部位置因素
        if head_position_deviation > 0.8 and warning_level.value < WarningLevel.HIGH.value:
            warning_level = WarningLevel.HIGH
        elif head_position_deviation > 0.5 and warning_level.value < WarningLevel.MEDIUM.value:
            warning_level = WarningLevel.MEDIUM
        
        # 只有在有预警时才返回数据
        if warning_level != WarningLevel.NONE:
            return {
                "warning_type": "DRIVER_ATTENTION",
                "level": warning_level.name,
                "level_value": warning_level.value,
                "eye_closed_time": eye_closed_time,
                "head_position_deviation": head_position_deviation
            }
        
        return None
    
    def check_speed_limit(self, current_speed, speed_limit):
        """
        超速预警
        
        参数:
            current_speed: 当前车速（km/h）
            speed_limit: 限速（km/h）
        
        返回:
            warning_data: 预警数据字典
        """
        if current_speed <= speed_limit:
            return None
        
        # 计算超速百分比
        overspeed_percent = (current_speed - speed_limit) / speed_limit * 100
        
        # 根据超速程度确定预警级别
        warning_level = WarningLevel.NONE
        
        if overspeed_percent > 50:
            warning_level = WarningLevel.CRITICAL
        elif overspeed_percent > 30:
            warning_level = WarningLevel.HIGH
        elif overspeed_percent > 15:
            warning_level = WarningLevel.MEDIUM
        elif overspeed_percent > 5:
            warning_level = WarningLevel.LOW
        
        # 只有在有预警时才返回数据
        if warning_level != WarningLevel.NONE:
            return {
                "warning_type": "SPEED_LIMIT",
                "level": warning_level.name,
                "level_value": warning_level.value,
                "current_speed": current_speed,
                "speed_limit": speed_limit,
                "overspeed_percent": overspeed_percent
            }
        
        return None
    
    def check_all_warnings(self, sensor_data):
        """
        综合检查所有预警
        
        参数:
            sensor_data: 传感器数据字典
        
        返回:
            warnings: 预警列表
        """
        warnings = []
        
        try:
            # 前向碰撞检查
            if "front_distance" in sensor_data:
                collision_warning = self.check_forward_collision(
                    sensor_data.get("front_distance"),
                    sensor_data.get("relative_speed")
                )
                if collision_warning:
                    warnings.append(collision_warning)
            
            # 车道偏离检查
            if "lane_position" in sensor_data:
                lane_warning = self.check_lane_departure(
                    sensor_data.get("lane_position"),
                    sensor_data.get("steering_angle")
                )
                if lane_warning:
                    warnings.append(lane_warning)
            
            # 驾驶员注意力检查
            if "eye_closed_time" in sensor_data and "head_position_deviation" in sensor_data:
                attention_warning = self.check_driver_attention(
                    sensor_data.get("eye_closed_time"),
                    sensor_data.get("head_position_deviation")
                )
                if attention_warning:
                    warnings.append(attention_warning)
            
            # 超速检查
            if "current_speed" in sensor_data and "speed_limit" in sensor_data:
                speed_warning = self.check_speed_limit(
                    sensor_data.get("current_speed"),
                    sensor_data.get("speed_limit")
                )
                if speed_warning:
                    warnings.append(speed_warning)
        
        except Exception as e:
            logger.error(f"预警检查异常: {e}")
        
        return warnings 