package com.sic.energy.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Data
@Document(collection = "user_profiles")
public class UserProfile {
    @Id
    private String id;
    
    private String userId;
    private String name;
    private String vehicleType;
    private Integer totalTrips;
    private Double totalDistance;
    private Double totalEnergySaved;
    private Double totalCarbonReduced;
    private List<String> preferredChargingStations;
    private List<String> drivingHabits;
    private Integer ecoScore;
} 