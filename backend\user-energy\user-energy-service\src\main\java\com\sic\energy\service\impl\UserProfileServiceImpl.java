package com.sic.energy.service.impl;

import com.sic.energy.model.UserProfile;
import com.sic.energy.repository.UserProfileRepository;
import com.sic.energy.service.UserProfileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class UserProfileServiceImpl implements UserProfileService {

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Override
    public Optional<UserProfile> getUserProfile(String userId) {
        return userProfileRepository.findByUserId(userId);
    }

    @Override
    public UserProfile saveUserProfile(UserProfile userProfile) {
        // 对于新用户，初始化eco得分
        if (userProfile.getEcoScore() == null) {
            userProfile.setEcoScore(60); // 默认初始分数
        }
        
        return userProfileRepository.save(userProfile);
    }

    @Override
    public List<Map<String, Object>> generateRecommendations(String userId) {
        Optional<UserProfile> userProfileOpt = userProfileRepository.findByUserId(userId);
        
        if (userProfileOpt.isEmpty()) {
            return Collections.emptyList();
        }
        
        UserProfile userProfile = userProfileOpt.get();
        List<Map<String, Object>> recommendations = new ArrayList<>();
        
        // 根据用户画像生成个性化建议
        if (userProfile.getEcoScore() < 60) {
            Map<String, Object> recommendation = new HashMap<>();
            recommendation.put("type", "energy_saving");
            recommendation.put("title", "提高能源利用效率");
            recommendation.put("content", "您的生态得分较低，建议尝试以下节能驾驶习惯：平稳加速，预见性减速，合理利用再生制动。");
            recommendation.put("importance", "high");
            recommendations.add(recommendation);
        }
        
        // 充电站推荐
        Map<String, Object> chargingRec = new HashMap<>();
        chargingRec.put("type", "charging_station");
        chargingRec.put("title", "推荐充电站");
        chargingRec.put("content", "根据您的位置和历史记录，我们为您推荐附近的高评分充电站。");
        chargingRec.put("stations", generateNearbyStations(userProfile));
        recommendations.add(chargingRec);
        
        // 维护提醒
        Map<String, Object> maintenanceRec = new HashMap<>();
        maintenanceRec.put("type", "maintenance");
        maintenanceRec.put("title", "电池健康检查提醒");
        maintenanceRec.put("content", "根据您的用车习惯，建议进行电池健康度检查，以保持最佳性能。");
        maintenanceRec.put("due_days", 15);
        recommendations.add(maintenanceRec);
        
        return recommendations;
    }

    @Override
    public boolean updateEcoScore(String userId, Integer newScore) {
        Optional<UserProfile> userProfileOpt = userProfileRepository.findByUserId(userId);
        
        if (userProfileOpt.isEmpty()) {
            return false;
        }
        
        UserProfile userProfile = userProfileOpt.get();
        userProfile.setEcoScore(newScore);
        userProfileRepository.save(userProfile);
        
        return true;
    }
    
    // 模拟生成附近充电站数据
    private List<Map<String, Object>> generateNearbyStations(UserProfile userProfile) {
        List<Map<String, Object>> stations = new ArrayList<>();
        
        // 这里应该调用充电站服务API获取真实数据
        // 暂时使用模拟数据
        for (int i = 1; i <= 3; i++) {
            Map<String, Object> station = new HashMap<>();
            station.put("id", "CS" + (10000 + i));
            station.put("name", "智能充电站 " + i);
            station.put("distance", 1.5 * i);
            station.put("available", 3);
            station.put("rating", 4.5);
            stations.add(station);
        }
        
        return stations;
    }
} 