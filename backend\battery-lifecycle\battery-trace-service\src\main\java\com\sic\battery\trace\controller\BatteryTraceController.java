package com.sic.battery.trace.controller;

import com.sic.battery.trace.model.Battery;
import com.sic.battery.trace.service.BatteryTraceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/battery/trace")
public class BatteryTraceController {

    @Autowired
    private BatteryTraceService batteryTraceService;

    @PostMapping
    public ResponseEntity<Battery> createBattery(@RequestBody Battery battery) {
        Battery createdBattery = batteryTraceService.createBattery(battery);
        return new ResponseEntity<>(createdBattery, HttpStatus.CREATED);
    }

    @GetMapping("/{batteryId}")
    public ResponseEntity<?> getBatteryById(@PathVariable String batteryId) {
        Optional<Battery> batteryOpt = batteryTraceService.getBatteryById(batteryId);
        if (batteryOpt.isPresent()) {
            return ResponseEntity.ok(batteryOpt.get());
        } else {
            Map<String, String> response = new HashMap<>();
            response.put("message", "电池不存在");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }
    }

    @GetMapping("/batch/{batchNumber}")
    public ResponseEntity<List<Battery>> getBatteriesByBatchNumber(@PathVariable String batchNumber) {
        List<Battery> batteries = batteryTraceService.getBatteriesByBatchNumber(batchNumber);
        return ResponseEntity.ok(batteries);
    }

    @GetMapping("/manufacturer/{manufacturer}")
    public ResponseEntity<List<Battery>> getBatteriesByManufacturer(@PathVariable String manufacturer) {
        List<Battery> batteries = batteryTraceService.getBatteriesByManufacturer(manufacturer);
        return ResponseEntity.ok(batteries);
    }

    @GetMapping("/model/{model}")
    public ResponseEntity<List<Battery>> getBatteriesByModel(@PathVariable String model) {
        List<Battery> batteries = batteryTraceService.getBatteriesByModel(model);
        return ResponseEntity.ok(batteries);
    }

    @GetMapping("/verify/{batteryId}")
    public ResponseEntity<Map<String, Object>> verifyBatteryOnChain(@PathVariable String batteryId) {
        boolean isVerified = batteryTraceService.verifyBatteryOnChain(batteryId);
        Map<String, Object> response = new HashMap<>();
        response.put("batteryId", batteryId);
        response.put("verified", isVerified);
        return ResponseEntity.ok(response);
    }
} 