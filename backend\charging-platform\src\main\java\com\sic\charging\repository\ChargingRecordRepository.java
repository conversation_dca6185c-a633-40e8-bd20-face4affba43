package com.sic.charging.repository;

import com.sic.charging.model.ChargingRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ChargingRecordRepository extends JpaRepository<ChargingRecord, Long> {
    
    List<ChargingRecord> findByUserId(String userId);
    
    List<ChargingRecord> findByVehicleId(String vehicleId);
    
    List<ChargingRecord> findByChargingPileId(Long pileId);
    
    @Query("SELECT cr FROM ChargingRecord cr WHERE cr.chargingPile.chargingStation.id = :stationId")
    List<ChargingRecord> findByStationId(@Param("stationId") Long stationId);
    
    List<ChargingRecord> findByStartTimeBetween(LocalDateTime start, LocalDateTime end);
    
    @Query("SELECT cr FROM ChargingRecord cr WHERE cr.status = :status AND cr.paymentStatus = :paymentStatus")
    List<ChargingRecord> findByStatusAndPaymentStatus(@Param("status") String status, @Param("paymentStatus") String paymentStatus);
    
    @Query("SELECT cr FROM ChargingRecord cr WHERE cr.userId = :userId AND cr.status = 'STARTED'")
    List<ChargingRecord> findActiveChargingByUserId(@Param("userId") String userId);
} 