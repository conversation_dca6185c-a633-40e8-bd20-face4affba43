package com.sic.charging.repository;

import com.sic.charging.model.ChargingStation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface ChargingStationRepository extends JpaRepository<ChargingStation, Long> {
    
    List<ChargingStation> findByIsOpenTrue();
    
    List<ChargingStation> findByOperatorName(String operatorName);
    
    @Query("SELECT cs FROM ChargingStation cs WHERE cs.availableChargers > 0 AND cs.isOpen = true")
    List<ChargingStation> findAvailableStations();
    
    @Query(value = "SELECT * FROM charging_stations cs WHERE " +
            "ST_Distance_Sphere(point(cs.longitude, cs.latitude), point(:longitude, :latitude)) <= :radiusMeters " +
            "AND cs.is_open = true " +
            "ORDER BY ST_Distance_Sphere(point(cs.longitude, cs.latitude), point(:longitude, :latitude))", 
            nativeQuery = true)
    List<ChargingStation> findNearbyStations(@Param("latitude") BigDecimal latitude, 
                                             @Param("longitude") BigDecimal longitude,
                                             @Param("radiusMeters") double radiusMeters);
} 