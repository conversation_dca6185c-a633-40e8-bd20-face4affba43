import { defineStore } from 'pinia'

interface AppState {
  sidebarCollapsed: boolean
  device: 'desktop' | 'mobile'
  theme: string
}

export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    sidebarCollapsed: false,
    device: 'desktop',
    theme: 'light'
  }),
  
  getters: {
    isMobile: (state) => state.device === 'mobile'
  },
  
  actions: {
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed
      // 保存到本地存储
      localStorage.setItem('sidebarStatus', this.sidebarCollapsed ? '1' : '0')
    },
    
    closeSidebar() {
      this.sidebarCollapsed = true
      localStorage.setItem('sidebarStatus', '1')
    },
    
    setDevice(device: 'desktop' | 'mobile') {
      this.device = device
    },
    
    setTheme(theme: string) {
      this.theme = theme
      // 保存到本地存储
      localStorage.setItem('theme', theme)
      
      // 应用主题
      document.documentElement.setAttribute('data-theme', theme)
    },
    
    // 初始化应用状态
    initAppState() {
      // 从本地存储恢复侧边栏状态
      const sidebarStatus = localStorage.getItem('sidebarStatus')
      if (sidebarStatus) {
        this.sidebarCollapsed = sidebarStatus === '1'
      }
      
      // 从本地存储恢复主题
      const theme = localStorage.getItem('theme')
      if (theme) {
        this.theme = theme
        document.documentElement.setAttribute('data-theme', theme)
      }
      
      // 检测设备类型
      const isMobile = window.innerWidth < 768
      this.device = isMobile ? 'mobile' : 'desktop'
      
      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        const isMobile = window.innerWidth < 768
        this.device = isMobile ? 'mobile' : 'desktop'
        
        if (isMobile && !this.sidebarCollapsed) {
          this.closeSidebar()
        }
      })
    }
  }
}) 