package com.sic.charging.model;

import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.List;

@Entity
@Table(name = "charging_stations")
@Data
public class ChargingStation {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String name;

    @Column(nullable = false)
    private String address;

    @Column(nullable = false, precision = 10, scale = 6)
    private BigDecimal latitude;

    @Column(nullable = false, precision = 10, scale = 6)
    private BigDecimal longitude;

    @Column(nullable = false)
    private String operatorName;

    @Column(nullable = false)
    private String contactPhone;

    private String description;

    @Column(nullable = false)
    private Boolean isOpen = true;

    @Column(nullable = false)
    private Integer totalChargers = 0;

    @Column(nullable = false)
    private Integer availableChargers = 0;

    @OneToMany(mappedBy = "chargingStation", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ChargingPile> chargingPiles;
} 