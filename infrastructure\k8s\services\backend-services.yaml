apiVersion: v1
kind: Service
metadata:
  name: battery-trace-service
  labels:
    app: battery-trace-service
spec:
  selector:
    app: battery-trace-service
  ports:
  - port: 8081
    targetPort: 8081
    name: http
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: charging-platform
  labels:
    app: charging-platform
spec:
  selector:
    app: charging-platform
  ports:
  - port: 8082
    targetPort: 8082
    name: http
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: tbox-service
  labels:
    app: tbox-service
spec:
  selector:
    app: tbox-service
  ports:
  - port: 8084
    targetPort: 8084
    name: http
  - port: 8085
    targetPort: 8085
    name: websocket
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: user-energy-service
  labels:
    app: user-energy-service
spec:
  selector:
    app: user-energy-service
  ports:
  - port: 8085
    targetPort: 8085
    name: http
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: adas-service
  labels:
    app: adas-service
spec:
  selector:
    app: adas-service
  ports:
  - port: 5000
    targetPort: 5000
    name: http
  type: ClusterIP 