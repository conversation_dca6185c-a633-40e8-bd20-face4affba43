package com.sic.energy.service;

import com.sic.energy.model.UserProfile;
import java.util.Optional;
import java.util.Map;
import java.util.List;

public interface UserProfileService {
    
    Optional<UserProfile> getUserProfile(String userId);
    
    UserProfile saveUserProfile(UserProfile userProfile);
    
    List<Map<String, Object>> generateRecommendations(String userId);
    
    boolean updateEcoScore(String userId, Integer newScore);
} 