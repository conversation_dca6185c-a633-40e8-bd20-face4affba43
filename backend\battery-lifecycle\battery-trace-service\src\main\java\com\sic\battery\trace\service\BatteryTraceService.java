package com.sic.battery.trace.service;

import com.sic.battery.trace.model.Battery;

import java.util.List;
import java.util.Optional;

public interface BatteryTraceService {
    /**
     * 创建新电池记录并上链
     * @param battery 电池信息
     * @return 创建的电池记录
     */
    Battery createBattery(Battery battery);

    /**
     * 根据电池ID查询电池
     * @param batteryId 电池ID
     * @return 电池信息
     */
    Optional<Battery> getBatteryById(String batteryId);

    /**
     * 根据批次号查询电池列表
     * @param batchNumber 批次号
     * @return 电池列表
     */
    List<Battery> getBatteriesByBatchNumber(String batchNumber);

    /**
     * 根据制造商查询电池列表
     * @param manufacturer 制造商
     * @return 电池列表
     */
    List<Battery> getBatteriesByManufacturer(String manufacturer);

    /**
     * 根据型号查询电池列表
     * @param model 型号
     * @return 电池列表
     */
    List<Battery> getBatteriesByModel(String model);

    /**
     * 验证电池溯源信息是否与区块链一致
     * @param batteryId 电池ID
     * @return 是否一致
     */
    boolean verifyBatteryOnChain(String batteryId);
} 