package com.sic.energy.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = "fleet_vehicles")
public class FleetVehicle {
    @Id
    private String id;
    
    private String fleetId;
    private String vin;
    private String model;
    private String plate;
    private String driverId;
    private Double mileage;
    private Integer batteryHealth;
    private String status; // ACTIVE, MAINTENANCE, INACTIVE
    private Double latitude;
    private Double longitude;
} 