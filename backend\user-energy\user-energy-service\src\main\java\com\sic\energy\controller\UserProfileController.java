package com.sic.energy.controller;

import com.sic.energy.model.UserProfile;
import com.sic.energy.service.UserProfileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/user/profile")
public class UserProfileController {

    @Autowired
    private UserProfileService userProfileService;
    
    @GetMapping("/{userId}")
    public ResponseEntity<UserProfile> getUserProfile(@PathVariable String userId) {
        return userProfileService.getUserProfile(userId)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @PostMapping
    public ResponseEntity<UserProfile> createUserProfile(@RequestBody UserProfile userProfile) {
        return ResponseEntity.ok(userProfileService.saveUserProfile(userProfile));
    }
    
    @PutMapping("/{userId}")
    public ResponseEntity<UserProfile> updateUserProfile(
            @PathVariable String userId, 
            @RequestBody UserProfile userProfile) {
        userProfile.setUserId(userId);
        return ResponseEntity.ok(userProfileService.saveUserProfile(userProfile));
    }
    
    @GetMapping("/{userId}/eco-score")
    public ResponseEntity<Integer> getUserEcoScore(@PathVariable String userId) {
        return userProfileService.getUserProfile(userId)
                .map(profile -> ResponseEntity.ok(profile.getEcoScore()))
                .orElse(ResponseEntity.notFound().build());
    }
    
    @PostMapping("/{userId}/recommend")
    public ResponseEntity<?> getRecommendations(@PathVariable String userId) {
        return ResponseEntity.ok(userProfileService.generateRecommendations(userId));
    }
} 