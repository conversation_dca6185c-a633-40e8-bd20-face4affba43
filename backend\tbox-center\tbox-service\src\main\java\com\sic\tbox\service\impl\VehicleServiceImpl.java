package com.sic.tbox.service.impl;

import com.sic.tbox.model.Vehicle;
import com.sic.tbox.repository.VehicleRepository;
import com.sic.tbox.service.VehicleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

@Service
@Slf4j
public class VehicleServiceImpl implements VehicleService {

    @Autowired
    private VehicleRepository vehicleRepository;
    
    @Autowired
    private SimpMessagingTemplate messagingTemplate;
    
    @Override
    public List<Vehicle> getAllVehicles() {
        return vehicleRepository.findAll();
    }
    
    @Override
    public Optional<Vehicle> getVehicleByVin(String vin) {
        return vehicleRepository.findByVin(vin);
    }
    
    @Override
    public Vehicle updateLocation(Vehicle vehicle) {
        Optional<Vehicle> existingVehicleOpt = vehicleRepository.findByVin(vehicle.getVin());
        
        if (existingVehicleOpt.isPresent()) {
            Vehicle existingVehicle = existingVehicleOpt.get();
            existingVehicle.setLatitude(vehicle.getLatitude());
            existingVehicle.setLongitude(vehicle.getLongitude());
            existingVehicle.setLastUpdateTime(LocalDateTime.now());
            
            // 保存更新后的车辆
            Vehicle savedVehicle = vehicleRepository.save(existingVehicle);
            
            // 推送位置更新消息
            Map<String, Object> locationUpdate = new HashMap<>();
            locationUpdate.put("vin", savedVehicle.getVin());
            locationUpdate.put("latitude", savedVehicle.getLatitude());
            locationUpdate.put("longitude", savedVehicle.getLongitude());
            locationUpdate.put("timestamp", savedVehicle.getLastUpdateTime());
            
            messagingTemplate.convertAndSend("/topic/location/" + savedVehicle.getVin(), locationUpdate);
            
            return savedVehicle;
        } else {
            log.warn("尝试更新不存在的车辆位置: {}", vehicle.getVin());
            return null;
        }
    }
    
    @Override
    public Vehicle updateDiagnostics(Vehicle vehicle) {
        Optional<Vehicle> existingVehicleOpt = vehicleRepository.findByVin(vehicle.getVin());
        
        if (existingVehicleOpt.isPresent()) {
            Vehicle existingVehicle = existingVehicleOpt.get();
            existingVehicle.setBatterySoc(vehicle.getBatterySoc());
            existingVehicle.setDiagnosticData(vehicle.getDiagnosticData());
            existingVehicle.setLastUpdateTime(LocalDateTime.now());
            
            // 保存更新后的车辆
            return vehicleRepository.save(existingVehicle);
        } else {
            log.warn("尝试更新不存在的车辆诊断数据: {}", vehicle.getVin());
            return null;
        }
    }
    
    @Override
    public Vehicle controlVehicle(Vehicle vehicle) {
        // 实现远程控制逻辑
        log.info("执行远程控制: {} - {}", vehicle.getVin(), vehicle.getDiagnosticData());
        
        // 这里可以添加实际的控制逻辑
        return vehicle;
    }
    
    @Override
    public boolean sendCommand(String vin, String command) {
        Optional<Vehicle> vehicleOpt = vehicleRepository.findByVin(vin);
        
        if (vehicleOpt.isPresent()) {
            Vehicle vehicle = vehicleOpt.get();
            
            if (!vehicle.isOnline()) {
                log.warn("车辆当前离线，无法发送命令: {}", vin);
                return false;
            }
            
            // 向车辆发送命令
            Map<String, Object> commandData = new HashMap<>();
            commandData.put("vin", vin);
            commandData.put("command", command);
            commandData.put("timestamp", LocalDateTime.now());
            
            messagingTemplate.convertAndSend("/topic/command/" + vin, commandData);
            log.info("命令已发送到车辆: {} - {}", vin, command);
            
            return true;
        } else {
            log.warn("尝试向不存在的车辆发送命令: {}", vin);
            return false;
        }
    }
    
    @Override
    public Optional<Map<String, Object>> getVehicleStatus(String vin) {
        return vehicleRepository.findByVin(vin).map(vehicle -> {
            Map<String, Object> status = new HashMap<>();
            status.put("vin", vehicle.getVin());
            status.put("model", vehicle.getModel());
            status.put("online", vehicle.isOnline());
            status.put("batterySoc", vehicle.getBatterySoc());
            status.put("latitude", vehicle.getLatitude());
            status.put("longitude", vehicle.getLongitude());
            status.put("lastUpdateTime", vehicle.getLastUpdateTime());
            
            // 解析诊断数据（假设诊断数据是JSON格式的字符串）
            if (vehicle.getDiagnosticData() != null && !vehicle.getDiagnosticData().isEmpty()) {
                try {
                    // 这里可以添加诊断数据解析逻辑
                    status.put("diagnostics", vehicle.getDiagnosticData());
                } catch (Exception e) {
                    log.error("解析诊断数据失败: {}", e.getMessage());
                }
            }
            
            return status;
        });
    }
} 