package com.sic.energy.controller;

import com.sic.energy.model.FleetVehicle;
import com.sic.energy.service.FleetManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/user/fleet")
public class FleetManagementController {

    @Autowired
    private FleetManagementService fleetManagementService;
    
    @GetMapping("/{fleetId}/vehicles")
    public ResponseEntity<List<FleetVehicle>> getFleetVehicles(@PathVariable String fleetId) {
        return ResponseEntity.ok(fleetManagementService.getFleetVehicles(fleetId));
    }
    
    @PostMapping("/vehicles")
    public ResponseEntity<FleetVehicle> addVehicleToFleet(@RequestBody FleetVehicle vehicle) {
        return ResponseEntity.ok(fleetManagementService.saveVehicle(vehicle));
    }
    
    @GetMapping("/vehicles/{vin}")
    public ResponseEntity<FleetVehicle> getVehicleByVin(@PathVariable String vin) {
        return fleetManagementService.getVehicleByVin(vin)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @PutMapping("/vehicles/{vin}/status")
    public ResponseEntity<FleetVehicle> updateVehicleStatus(
            @PathVariable String vin, 
            @RequestParam String status) {
        
        return fleetManagementService.updateVehicleStatus(vin, status)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @GetMapping("/{fleetId}/analytics")
    public ResponseEntity<Map<String, Object>> getFleetAnalytics(@PathVariable String fleetId) {
        Map<String, Object> analytics = fleetManagementService.getFleetAnalytics(fleetId);
        return ResponseEntity.ok(analytics);
    }
    
    @GetMapping("/{fleetId}/maintenance-schedule")
    public ResponseEntity<List<Map<String, Object>>> getMaintenanceSchedule(@PathVariable String fleetId) {
        List<Map<String, Object>> schedule = fleetManagementService.getMaintenanceSchedule(fleetId);
        return ResponseEntity.ok(schedule);
    }
} 