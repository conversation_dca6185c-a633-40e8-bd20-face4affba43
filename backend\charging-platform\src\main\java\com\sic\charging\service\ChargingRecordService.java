package com.sic.charging.service;

import com.sic.charging.model.ChargingRecord;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface ChargingRecordService {
    
    ChargingRecord startCharging(String userId, String vehicleId, Long pileId);
    
    ChargingRecord endCharging(Long recordId, BigDecimal energyConsumed);
    
    Optional<ChargingRecord> getRecordById(Long id);
    
    List<ChargingRecord> getRecordsByUserId(String userId);
    
    List<ChargingRecord> getRecordsByVehicleId(String vehicleId);
    
    List<ChargingRecord> getRecordsByStationId(Long stationId);
    
    List<ChargingRecord> getRecordsByDateRange(LocalDateTime start, LocalDateTime end);
    
    ChargingRecord updatePaymentStatus(Long recordId, String paymentStatus, String paymentMethod, String transactionId);
    
    List<ChargingRecord> getActiveChargingByUserId(String userId);
} 