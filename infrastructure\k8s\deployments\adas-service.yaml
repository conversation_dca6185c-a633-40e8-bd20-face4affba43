apiVersion: apps/v1
kind: Deployment
metadata:
  name: adas-service
  labels:
    app: adas-service
    tier: backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: adas-service
  template:
    metadata:
      labels:
        app: adas-service
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/path: "/metrics"
        prometheus.io/port: "5000"
    spec:
      containers:
      - name: adas-service
        image: sic/adas-service:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 5000
        env:
        - name: FLASK_ENV
          value: "production"
        - name: PYTHONUNBUFFERED
          value: "1"
        - name: LOG_LEVEL
          value: "INFO"
        resources:
          limits:
            cpu: "2"
            memory: "2Gi"
          requests:
            cpu: "1"
            memory: "1Gi"
        readinessProbe:
          httpGet:
            path: /api/adas/status
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /api/adas/status
            port: 5000
          initialDelaySeconds: 60
          periodSeconds: 30
        volumeMounts:
        - name: adas-config
          mountPath: /app/config
        - name: adas-models
          mountPath: /app/models
      volumes:
      - name: adas-config
        configMap:
          name: adas-config
      - name: adas-models
        persistentVolumeClaim:
          claimName: adas-models-pvc 