package com.sic.charging.model;

import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "charging_records")
@Data
public class ChargingRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String userId;

    @Column(nullable = false)
    private String vehicleId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "charging_pile_id", nullable = false)
    private ChargingPile chargingPile;

    @Column(nullable = false)
    private LocalDateTime startTime;

    private LocalDateTime endTime;

    @Column(nullable = false)
    private String status; // STARTED, COMPLETED, INTERRUPTED

    private BigDecimal energyConsumed; // 消耗的电量，单位kWh

    private BigDecimal totalAmount; // 总费用

    private String paymentStatus; // PENDING, PAID, FAILED

    private String paymentMethod; // WECHAT, ALIPAY, CREDITCARD

    private String transactionId; // 支付交易ID
} 