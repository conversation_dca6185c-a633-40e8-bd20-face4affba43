package com.sic.charging.controller;

import com.sic.charging.model.ChargingStation;
import com.sic.charging.service.ChargingStationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/charging/stations")
@Slf4j
public class ChargingStationController {

    @Autowired
    private ChargingStationService chargingStationService;

    @PostMapping
    public ResponseEntity<ChargingStation> createStation(@Valid @RequestBody ChargingStation station) {
        ChargingStation createdStation = chargingStationService.createStation(station);
        return new ResponseEntity<>(createdStation, HttpStatus.CREATED);
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getStationById(@PathVariable Long id) {
        return chargingStationService.getStationById(id)
                .map(station -> ResponseEntity.ok(station))
                .orElseGet(() -> {
                    Map<String, String> response = new HashMap<>();
                    response.put("message", "充电站不存在");
                    return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
                });
    }

    @GetMapping
    public ResponseEntity<List<ChargingStation>> getAllStations() {
        List<ChargingStation> stations = chargingStationService.getAllStations();
        return ResponseEntity.ok(stations);
    }

    @GetMapping("/available")
    public ResponseEntity<List<ChargingStation>> getAvailableStations() {
        List<ChargingStation> stations = chargingStationService.getAvailableStations();
        return ResponseEntity.ok(stations);
    }

    @GetMapping("/nearby")
    public ResponseEntity<List<ChargingStation>> getNearbyStations(
            @RequestParam BigDecimal latitude,
            @RequestParam BigDecimal longitude,
            @RequestParam(defaultValue = "5.0") double radius) {
        List<ChargingStation> stations = chargingStationService.getNearbyStations(latitude, longitude, radius);
        return ResponseEntity.ok(stations);
    }

    @PutMapping("/{id}")
    public ResponseEntity<?> updateStation(@PathVariable Long id, @Valid @RequestBody ChargingStation station) {
        try {
            ChargingStation updatedStation = chargingStationService.updateStation(id, station);
            return ResponseEntity.ok(updatedStation);
        } catch (RuntimeException e) {
            Map<String, String> response = new HashMap<>();
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteStation(@PathVariable Long id) {
        try {
            chargingStationService.deleteStation(id);
            Map<String, String> response = new HashMap<>();
            response.put("message", "充电站已删除");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> response = new HashMap<>();
            response.put("message", "删除充电站失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @PatchMapping("/{id}/availability")
    public ResponseEntity<?> updateStationAvailability(
            @PathVariable Long id,
            @RequestParam int availableChargers,
            @RequestParam boolean isOpen) {
        try {
            chargingStationService.updateStationAvailability(id, availableChargers, isOpen);
            Map<String, String> response = new HashMap<>();
            response.put("message", "充电站可用性已更新");
            return ResponseEntity.ok(response);
        } catch (RuntimeException e) {
            Map<String, String> response = new HashMap<>();
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }
    }
} 