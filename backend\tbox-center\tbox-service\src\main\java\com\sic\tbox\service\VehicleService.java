package com.sic.tbox.service;

import com.sic.tbox.model.Vehicle;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface VehicleService {
    
    List<Vehicle> getAllVehicles();
    
    Optional<Vehicle> getVehicleByVin(String vin);
    
    Vehicle updateLocation(Vehicle vehicle);
    
    Vehicle updateDiagnostics(Vehicle vehicle);
    
    Vehicle controlVehicle(Vehicle vehicle);
    
    boolean sendCommand(String vin, String command);
    
    Optional<Map<String, Object>> getVehicleStatus(String vin);
} 