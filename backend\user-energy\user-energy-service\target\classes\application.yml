server:
  port: 8085

spring:
  application:
    name: user-energy-service
  data:
    mongodb:
      host: localhost
      port: 27017
      database: user_energy_db
      username: root
      password: root

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
  instance:
    prefer-ip-address: true

# 启用Actuator监控端点
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics

# 日志配置  
logging:
  level:
    root: INFO
    com.sic.energy: DEBUG
    org.springframework.data.mongodb: INFO 