import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue')
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('../views/Dashboard.vue'),
    children: [
      {
        path: '',
        redirect: '/dashboard/home'
      },
      {
        path: 'home',
        name: 'Home',
        component: () => import('../views/Home.vue')
      },
      {
        path: 'battery-list',
        name: 'BatteryList',
        component: () => import('../views/battery/BatteryList.vue')
      },
      {
        path: 'battery-create',
        name: 'BatteryCreate',
        component: () => import('../views/battery/BatteryCreate.vue')
      },
      {
        path: 'battery-detail/:id',
        name: 'BatteryDetail',
        component: () => import('../views/battery/BatteryDetail.vue')
      },
      {
        path: 'batch-management',
        name: 'BatchManagement',
        component: () => import('../views/batch/BatchManagement.vue')
      },
      {
        path: 'blockchain-verify',
        name: 'BlockchainVerify',
        component: () => import('../views/blockchain/BlockchainVerify.vue')
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../views/NotFound.vue')
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')
  if (to.path !== '/login' && !token) {
    next('/login')
  } else {
    next()
  }
})

export default router 