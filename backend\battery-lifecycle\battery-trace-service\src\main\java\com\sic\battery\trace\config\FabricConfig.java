package com.sic.battery.trace.config;

import lombok.Data;
import org.hyperledger.fabric.gateway.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;

import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;

@Configuration
@Data
public class FabricConfig {
    @Value("${fabric.network-config}")
    private Resource networkConfigResource;

    @Value("${fabric.channel-name}")
    private String channelName;

    @Value("${fabric.chaincode-name}")
    private String chaincodeName;

    @Value("${fabric.wallet-path}")
    private String walletPath;

    @Value("${fabric.user-name}")
    private String userName;

    @Bean
    public Gateway gateway() throws IOException {
        // 加载网络配置文件
        Path networkConfigPath = Paths.get(networkConfigResource.getURI());
        
        // 加载钱包
        Path walletDirectory = Paths.get(walletPath);
        Wallet wallet = Wallets.newFileSystemWallet(walletDirectory);
        
        // 连接网络
        Gateway.Builder builder = Gateway.createBuilder()
                .identity(wallet, userName)
                .networkConfig(networkConfigPath);
        
        return builder.connect();
    }

    @Bean
    public Network network(Gateway gateway) {
        return gateway.getNetwork(channelName);
    }

    @Bean
    public Contract contract(Network network) {
        return network.getContract(chaincodeName);
    }
} 