apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-energy-service
  labels:
    app: user-energy-service
    tier: backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: user-energy-service
  template:
    metadata:
      labels:
        app: user-energy-service
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/path: "/actuator/prometheus"
        prometheus.io/port: "8085"
    spec:
      containers:
      - name: user-energy-service
        image: sic/user-energy-service:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8085
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE
          value: "http://eureka-server:8761/eureka/"
        - name: SPRING_DATA_MONGODB_HOST
          value: "mongodb"
        - name: SPRING_DATA_MONGODB_PORT
          value: "27017"
        - name: SPRING_DATA_MONGODB_DATABASE
          value: "user_energy_db"
        - name: SPRING_DATA_MONGODB_USERNAME
          valueFrom:
            secretKeyRef:
              name: mongodb-credentials
              key: username
        - name: SPRING_DATA_MONGODB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mongodb-credentials
              key: password
        resources:
          limits:
            cpu: "1"
            memory: "1Gi"
          requests:
            cpu: "500m"
            memory: "512Mi"
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8085
          initialDelaySeconds: 60
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8085
          initialDelaySeconds: 120
          periodSeconds: 30 