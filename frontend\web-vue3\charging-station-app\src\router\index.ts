import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
import { isAuthenticated } from '@/utils/auth'
import { useUserStore } from '@/store/user'
import { ElMessage } from 'element-plus'

// 路由配置
const routes: Array<RouteRecordRaw> = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: { title: '登录', requiresAuth: false }
  },
  {
    path: '/',
    component: () => import('@/views/layout/index.vue'),
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: { title: '首页', icon: 'dashboard', requiresAuth: true }
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/profile/index.vue'),
        meta: { title: '个人中心', requiresAuth: true }
      },
      {
        path: 'settings',
        name: 'Settings',
        component: () => import('@/views/settings/index.vue'),
        meta: { title: '系统设置', requiresAuth: true }
      }
    ]
  },
  // 充电站管理相关路由
  {
    path: '/stations',
    component: () => import('@/views/layout/index.vue'),
    redirect: '/stations/map',
    meta: { title: '充电站管理', requiresAuth: true },
    children: [
      {
        path: 'map',
        name: 'StationMap',
        component: () => import('@/views/stations/map.vue'),
        meta: { title: '地图视图', requiresAuth: true }
      },
      {
        path: 'list',
        name: 'StationList',
        component: () => import('@/views/stations/list.vue'),
        meta: { title: '列表视图', requiresAuth: true }
      },
      {
        path: 'add',
        name: 'StationAdd',
        component: () => import('@/views/stations/add.vue'),
        meta: { title: '添加充电站', requiresAuth: true }
      },
      {
        path: 'edit/:id',
        name: 'StationEdit',
        component: () => import('@/views/stations/edit.vue'),
        meta: { title: '编辑充电站', requiresAuth: true }
      },
      {
        path: 'detail/:id',
        name: 'StationDetail',
        component: () => import('@/views/stations/detail.vue'),
        meta: { title: '充电站详情', requiresAuth: true }
      }
    ]
  },
  // 充电桩管理相关路由
  {
    path: '/chargers',
    component: () => import('@/views/layout/index.vue'),
    redirect: '/chargers/list',
    meta: { title: '充电桩管理', requiresAuth: true },
    children: [
      {
        path: 'list',
        name: 'ChargerList',
        component: () => import('@/views/chargers/list.vue'),
        meta: { title: '充电桩列表', requiresAuth: true }
      },
      {
        path: 'status',
        name: 'ChargerStatus',
        component: () => import('@/views/chargers/status.vue'),
        meta: { title: '状态监控', requiresAuth: true }
      },
      {
        path: 'maintenance',
        name: 'ChargerMaintenance',
        component: () => import('@/views/chargers/maintenance.vue'),
        meta: { title: '维护管理', requiresAuth: true }
      }
    ]
  },
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: { title: '404', requiresAuth: false }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  document.title = `${to.meta.title} - 充电站管理系统`
  
  // 检查是否需要登录
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  
  if (requiresAuth) {
    // 检查是否已登录
    if (isAuthenticated()) {
      const userStore = useUserStore()
      
      // 如果没有用户信息，获取用户信息
      if (!userStore.userId) {
        try {
          await userStore.getInfo()
          next()
        } catch (error) {
          // 获取用户信息失败，可能是token过期
          userStore.logout()
          ElMessage.error('登录状态已过期，请重新登录')
          next(`/login?redirect=${to.path}`)
        }
      } else {
        next()
      }
    } else {
      // 未登录，跳转到登录页
      ElMessage.warning('请先登录')
      next(`/login?redirect=${to.path}`)
    }
  } else {
    // 不需要登录的页面直接放行
    next()
  }
})

export default router 