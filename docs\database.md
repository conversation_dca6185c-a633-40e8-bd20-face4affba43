# 数据库设计

## 一、数据库选型与用途
- MySQL：生产溯源、订单、用户、权限等结构化数据
- PostgreSQL/PostGIS：工单、地理信息、车队管理
- InfluxDB：高频时序数据（如BMS电池数据）
- MongoDB：充电桩、用户行为日志、推荐系统
- Redis：缓存、支付状态、健康度预测结果

## 二、典型表设计建议
- 用户表（user）：用户ID、姓名、手机号、角色、认证信息等
- 电池表（battery）：电池ID、生产批次、原材料、工艺参数、区块链哈希等
- 充电桩表（charger）：充电桩ID、位置、状态、运营商等
- 工单表（work_order）：工单ID、类型、状态、关联电池/车辆/企业等
- 订单表（order）：订单ID、用户ID、金额、支付状态、发票等
- 时序数据表（InfluxDB）：时间、设备ID、电压、电流、温度等

详细ER图和表结构请结合实际业务补充。 