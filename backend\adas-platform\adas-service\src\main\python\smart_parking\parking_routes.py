from flask import Blueprint, request, jsonify
import logging
import time
from .parking_controller import ParkingController, ParkingMode

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建蓝图
parking_bp = Blueprint('parking', __name__, url_prefix='/api/adas/parking')

# 创建泊车控制器实例
parking_controller = ParkingController()

@parking_bp.route('/status', methods=['GET'])
def get_status():
    """获取泊车状态"""
    return jsonify({
        "status": "success",
        "timestamp": time.time(),
        "parking_status": parking_controller.get_status()
    })

@parking_bp.route('/mode', methods=['POST'])
def set_mode():
    """设置泊车模式"""
    try:
        data = request.json
        if not data or "mode" not in data:
            return jsonify({
                "status": "error",
                "message": "未指定泊车模式"
            }), 400
        
        mode = data["mode"]
        if parking_controller.set_mode(mode):
            return jsonify({
                "status": "success",
                "message": f"泊车模式设置为 {mode}",
                "current_status": parking_controller.get_status()
            })
        else:
            return jsonify({
                "status": "error",
                "message": f"无效的泊车模式: {mode}"
            }), 400
    except Exception as e:
        logger.error(f"设置泊车模式异常: {e}")
        return jsonify({
            "status": "error",
            "message": f"设置泊车模式异常: {str(e)}"
        }), 500

@parking_bp.route('/search', methods=['POST'])
def search_parking_spot():
    """搜索停车位"""
    try:
        data = request.json or {}
        min_width = data.get("min_width", 2.5)
        min_depth = data.get("min_depth", 5.0)
        
        spot = parking_controller.search_parking_spot(min_width, min_depth)
        
        if spot:
            return jsonify({
                "status": "success",
                "message": "找到合适的停车位",
                "parking_spot": spot,
                "current_status": parking_controller.get_status()
            })
        else:
            return jsonify({
                "status": "warning",
                "message": "未找到合适的停车位，请继续搜索"
            }), 404
    except Exception as e:
        logger.error(f"搜索停车位异常: {e}")
        return jsonify({
            "status": "error",
            "message": f"搜索停车位异常: {str(e)}"
        }), 500

@parking_bp.route('/plan', methods=['POST'])
def plan_path():
    """规划泊车路径"""
    try:
        # 可以接收一些规划参数
        data = request.json or {}
        
        # 先设置当前位置（通常由车辆提供）
        current_position = data.get("current_position", [0.0, 0.0])
        parking_controller.set_position(current_position)
        
        path = parking_controller.plan_path()
        
        if path:
            return jsonify({
                "status": "success",
                "message": "泊车路径规划成功",
                "path_points": len(path),
                "path_preview": path[:2],  # 仅返回前几个点作为预览
                "current_status": parking_controller.get_status()
            })
        else:
            return jsonify({
                "status": "error",
                "message": "泊车路径规划失败"
            }), 400
    except Exception as e:
        logger.error(f"规划泊车路径异常: {e}")
        return jsonify({
            "status": "error",
            "message": f"规划泊车路径异常: {str(e)}"
        }), 500

@parking_bp.route('/execute', methods=['POST'])
def execute_parking():
    """执行泊车过程"""
    try:
        # 启动泊车执行
        if parking_controller.execute_parking():
            return jsonify({
                "status": "success",
                "message": "泊车执行完成",
                "current_status": parking_controller.get_status()
            })
        else:
            return jsonify({
                "status": "error",
                "message": "泊车执行失败"
            }), 400
    except Exception as e:
        logger.error(f"执行泊车异常: {e}")
        return jsonify({
            "status": "error",
            "message": f"执行泊车异常: {str(e)}"
        }), 500

@parking_bp.route('/abort', methods=['POST'])
def abort_parking():
    """中止泊车过程"""
    try:
        parking_controller.abort_parking()
        return jsonify({
            "status": "success",
            "message": "泊车操作已中止",
            "current_status": parking_controller.get_status()
        })
    except Exception as e:
        logger.error(f"中止泊车异常: {e}")
        return jsonify({
            "status": "error",
            "message": f"中止泊车异常: {str(e)}"
        }), 500

@parking_bp.route('/sensors', methods=['POST'])
def update_sensors():
    """更新传感器数据"""
    try:
        data = request.json
        if not data:
            return jsonify({
                "status": "error",
                "message": "未提供传感器数据"
            }), 400
        
        parking_controller.update_sensors(data)
        return jsonify({
            "status": "success",
            "message": "传感器数据已更新"
        })
    except Exception as e:
        logger.error(f"更新传感器数据异常: {e}")
        return jsonify({
            "status": "error",
            "message": f"更新传感器数据异常: {str(e)}"
        }), 500

def register_parking_routes(app):
    """注册泊车路由到应用"""
    app.register_blueprint(parking_bp)
    logger.info("智能泊车路由已注册") 