server:
  port: 8081

spring:
  application:
    name: auth-service
  datasource:
    url: *******************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0

# 日志配置
logging:
  level:
    root: INFO
    com.sic: DEBUG

# 自定义配置
jwt:
  secret: sic_secret_key_2024
  expiration: 86400000  # 24小时 