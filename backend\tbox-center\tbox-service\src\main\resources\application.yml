server:
  port: 8084

spring:
  application:
    name: tbox-service
  datasource:
    url: *******************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    database-platform: org.hibernate.dialect.MySQL8Dialect

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
  instance:
    prefer-ip-address: true

# WebSocket配置
websocket:
  allowedOrigins: "*"

# 日志配置  
logging:
  level:
    root: INFO
    com.sic.tbox: DEBUG
    org.springframework.web.socket: DEBUG 