package com.sic.energy.service.impl;

import com.sic.energy.model.FleetVehicle;
import com.sic.energy.repository.FleetVehicleRepository;
import com.sic.energy.service.FleetManagementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FleetManagementServiceImpl implements FleetManagementService {

    @Autowired
    private FleetVehicleRepository fleetVehicleRepository;

    @Override
    public List<FleetVehicle> getFleetVehicles(String fleetId) {
        return fleetVehicleRepository.findByFleetId(fleetId);
    }

    @Override
    public FleetVehicle saveVehicle(FleetVehicle vehicle) {
        return fleetVehicleRepository.save(vehicle);
    }

    @Override
    public Optional<FleetVehicle> getVehicleByVin(String vin) {
        return fleetVehicleRepository.findByVin(vin);
    }

    @Override
    public Optional<FleetVehicle> updateVehicleStatus(String vin, String status) {
        Optional<FleetVehicle> vehicleOpt = fleetVehicleRepository.findByVin(vin);
        
        if (vehicleOpt.isPresent()) {
            FleetVehicle vehicle = vehicleOpt.get();
            vehicle.setStatus(status);
            return Optional.of(fleetVehicleRepository.save(vehicle));
        }
        
        return Optional.empty();
    }

    @Override
    public Map<String, Object> getFleetAnalytics(String fleetId) {
        List<FleetVehicle> vehicles = fleetVehicleRepository.findByFleetId(fleetId);
        
        Map<String, Object> analytics = new HashMap<>();
        
        // 基本统计
        analytics.put("totalVehicles", vehicles.size());
        analytics.put("activeVehicles", vehicles.stream().filter(v -> "ACTIVE".equals(v.getStatus())).count());
        analytics.put("maintenanceVehicles", vehicles.stream().filter(v -> "MAINTENANCE".equals(v.getStatus())).count());
        analytics.put("inactiveVehicles", vehicles.stream().filter(v -> "INACTIVE".equals(v.getStatus())).count());
        
        // 电池健康度分布
        Map<String, Long> batteryHealthDistribution = new HashMap<>();
        batteryHealthDistribution.put("excellent", vehicles.stream().filter(v -> v.getBatteryHealth() >= 90).count());
        batteryHealthDistribution.put("good", vehicles.stream().filter(v -> v.getBatteryHealth() >= 70 && v.getBatteryHealth() < 90).count());
        batteryHealthDistribution.put("fair", vehicles.stream().filter(v -> v.getBatteryHealth() >= 50 && v.getBatteryHealth() < 70).count());
        batteryHealthDistribution.put("poor", vehicles.stream().filter(v -> v.getBatteryHealth() < 50).count());
        analytics.put("batteryHealthDistribution", batteryHealthDistribution);
        
        // 总里程
        double totalMileage = vehicles.stream().mapToDouble(FleetVehicle::getMileage).sum();
        analytics.put("totalMileage", totalMileage);
        analytics.put("averageMileage", vehicles.isEmpty() ? 0 : totalMileage / vehicles.size());
        
        return analytics;
    }

    @Override
    public List<Map<String, Object>> getMaintenanceSchedule(String fleetId) {
        List<FleetVehicle> vehicles = fleetVehicleRepository.findByFleetId(fleetId);
        
        // 根据里程和电池健康度为每辆车生成维护计划
        return vehicles.stream().map(vehicle -> {
            Map<String, Object> schedule = new HashMap<>();
            schedule.put("vin", vehicle.getVin());
            schedule.put("plate", vehicle.getPlate());
            schedule.put("model", vehicle.getModel());
            
            // 计算下一次维护日期（简化逻辑，根据里程和电池健康度）
            LocalDate nextMaintenance;
            if (vehicle.getBatteryHealth() < 50) {
                nextMaintenance = LocalDate.now().plusDays(7); // 紧急检查
            } else if (vehicle.getBatteryHealth() < 70) {
                nextMaintenance = LocalDate.now().plusDays(30); // 一个月内
            } else if (vehicle.getMileage() > 50000) {
                nextMaintenance = LocalDate.now().plusDays(60); // 两个月内
            } else {
                nextMaintenance = LocalDate.now().plusDays(90); // 三个月内
            }
            schedule.put("nextMaintenanceDate", nextMaintenance);
            
            // 推荐的维护项目
            List<String> recommendedServices = new ArrayList<>();
            if (vehicle.getBatteryHealth() < 70) {
                recommendedServices.add("电池健康度检查");
            }
            if (vehicle.getMileage() > 20000) {
                recommendedServices.add("冷却系统检查");
            }
            if (vehicle.getMileage() > 40000) {
                recommendedServices.add("电机系统检查");
            }
            recommendedServices.add("标准电气系统检查");
            schedule.put("recommendedServices", recommendedServices);
            
            // 预估维护费用
            double estimatedCost = 500.0; // 基础费用
            if (vehicle.getBatteryHealth() < 60) {
                estimatedCost += 800.0; // 电池进一步检测
            }
            if (vehicle.getMileage() > 50000) {
                estimatedCost += 500.0; // 高里程额外检查
            }
            schedule.put("estimatedCost", estimatedCost);
            
            return schedule;
        }).collect(Collectors.toList());
    }
} 