package main

import (
	"encoding/json"
	"fmt"

	"github.com/hyperledger/fabric-contract-api-go/contractapi"
)

// BatteryTrace 链码实现
type BatteryTrace struct {
	contractapi.Contract
}

// Battery 结构体定义
type Battery struct {
	BatteryID        string `json:"batteryId"`
	BatchNumber      string `json:"batchNumber"`
	Manufacturer     string `json:"manufacturer"`
	ProductionDate   string `json:"productionDate"`
	Model            string `json:"model"`
	Capacity         float64 `json:"capacity"`
	Voltage          float64 `json:"voltage"`
	Weight           float64 `json:"weight"`
	RawMaterials     string `json:"rawMaterials"`
	ProductionParams string `json:"productionParams"`
	TestResults      string `json:"testResults"`
}

// InitLedger 初始化账本
func (s *BatteryTrace) InitLedger(ctx contractapi.TransactionContextInterface) error {
	return nil
}

// CreateBattery 创建电池记录
func (s *BatteryTrace) CreateBattery(ctx contractapi.TransactionContextInterface, batteryID string, batteryJSON string) (string, error) {
	exists, err := s.BatteryExists(ctx, batteryID)
	if err != nil {
		return "", fmt.Errorf("查询电池记录失败: %v", err)
	}
	if exists {
		return "", fmt.Errorf("电池ID已存在: %s", batteryID)
	}

	// 验证电池JSON
	var battery Battery
	err = json.Unmarshal([]byte(batteryJSON), &battery)
	if err != nil {
		return "", fmt.Errorf("电池JSON格式错误: %v", err)
	}

	// 存储电池记录
	err = ctx.GetStub().PutState(batteryID, []byte(batteryJSON))
	if err != nil {
		return "", fmt.Errorf("存储电池记录失败: %v", err)
	}

	// 返回交易ID
	return ctx.GetStub().GetTxID(), nil
}

// GetBattery 获取电池记录
func (s *BatteryTrace) GetBattery(ctx contractapi.TransactionContextInterface, batteryID string) (string, error) {
	batteryJSON, err := ctx.GetStub().GetState(batteryID)
	if err != nil {
		return "", fmt.Errorf("获取电池记录失败: %v", err)
	}
	if batteryJSON == nil {
		return "", fmt.Errorf("电池ID不存在: %s", batteryID)
	}
	return string(batteryJSON), nil
}

// BatteryExists 检查电池是否存在
func (s *BatteryTrace) BatteryExists(ctx contractapi.TransactionContextInterface, batteryID string) (bool, error) {
	batteryJSON, err := ctx.GetStub().GetState(batteryID)
	if err != nil {
		return false, fmt.Errorf("查询电池记录失败: %v", err)
	}
	return batteryJSON != nil, nil
}

// GetBatteriesByBatch 根据批次号查询电池
func (s *BatteryTrace) GetBatteriesByBatch(ctx contractapi.TransactionContextInterface, batchNumber string) ([]*Battery, error) {
	queryString := fmt.Sprintf(`{"selector":{"batchNumber":"%s"}}`, batchNumber)
	return s.queryBatteries(ctx, queryString)
}

// GetBatteriesByManufacturer 根据制造商查询电池
func (s *BatteryTrace) GetBatteriesByManufacturer(ctx contractapi.TransactionContextInterface, manufacturer string) ([]*Battery, error) {
	queryString := fmt.Sprintf(`{"selector":{"manufacturer":"%s"}}`, manufacturer)
	return s.queryBatteries(ctx, queryString)
}

// 通用查询方法
func (s *BatteryTrace) queryBatteries(ctx contractapi.TransactionContextInterface, queryString string) ([]*Battery, error) {
	resultsIterator, err := ctx.GetStub().GetQueryResult(queryString)
	if err != nil {
		return nil, err
	}
	defer resultsIterator.Close()

	var batteries []*Battery
	for resultsIterator.HasNext() {
		queryResponse, err := resultsIterator.Next()
		if err != nil {
			return nil, err
		}

		var battery Battery
		err = json.Unmarshal(queryResponse.Value, &battery)
		if err != nil {
			return nil, err
		}
		batteries = append(batteries, &battery)
	}

	return batteries, nil
}

func main() {
	chaincode, err := contractapi.NewChaincode(&BatteryTrace{})
	if err != nil {
		fmt.Printf("创建电池溯源链码失败: %v", err)
		return
	}

	if err := chaincode.Start(); err != nil {
		fmt.Printf("启动电池溯源链码失败: %v", err)
	}
} 