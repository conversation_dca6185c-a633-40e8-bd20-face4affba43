import numpy as np
import logging
import time
from enum import Enum

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ParkingMode(Enum):
    """泊车模式枚举"""
    PERPENDICULAR = 1  # 垂直泊车
    PARALLEL = 2       # 平行泊车
    SLANT = 3          # 斜向泊车

class ParkingStage(Enum):
    """泊车阶段枚举"""
    SEARCHING = 1      # 搜索车位
    POSITIONING = 2    # 定位
    MANEUVERING = 3    # 入位操作
    ADJUSTING = 4      # 微调
    COMPLETED = 5      # 完成
    FAILED = 6         # 失败

class ParkingController:
    """智能泊车控制器"""
    
    def __init__(self):
        self.mode = None
        self.stage = ParkingStage.SEARCHING
        self.parking_spot = None
        self.path = []
        self.current_position = None
        self.sensors = {}
        logger.info("初始化智能泊车控制器")
        
    def set_mode(self, mode):
        """设置泊车模式"""
        if isinstance(mode, ParkingMode):
            self.mode = mode
        elif isinstance(mode, str):
            mode_map = {
                "perpendicular": ParkingMode.PERPENDICULAR,
                "parallel": ParkingMode.PARALLEL,
                "slant": ParkingMode.SLANT
            }
            self.mode = mode_map.get(mode.lower())
        
        if self.mode:
            logger.info(f"泊车模式设置为: {self.mode.name}")
            return True
        else:
            logger.error(f"无效的泊车模式: {mode}")
            return False
    
    def update_sensors(self, sensor_data):
        """更新传感器数据"""
        self.sensors = sensor_data
        return True
    
    def set_position(self, position):
        """更新当前位置"""
        self.current_position = position
        return True
    
    def search_parking_spot(self, min_width=2.5, min_depth=5.0):
        """
        搜索合适的停车位
        
        参数:
            min_width: 最小宽度要求(m)
            min_depth: 最小深度要求(m) - 针对垂直和斜向停车
            
        返回:
            parking_spot: 停车位信息字典，如果找到
            None: 如果未找到
        """
        logger.info(f"开始搜索停车位，模式: {self.mode.name if self.mode else '未设置'}")
        
        if not self.mode:
            logger.error("未设置泊车模式，无法搜索停车位")
            return None
        
        # 这里通常会基于雷达、摄像头等数据搜索停车位
        # 以下是模拟实现
        
        # 模拟发现停车位
        self.stage = ParkingStage.POSITIONING
        
        # 根据不同泊车模式，需要不同大小的停车位
        required_width = min_width
        required_depth = min_depth
        
        if self.mode == ParkingMode.PARALLEL:
            required_width = min_depth  # 平行泊车宽度要求更大
            required_depth = min_width
        
        # 假设我们找到了一个停车位
        self.parking_spot = {
            "position": [10.0, 2.0],  # 相对于车辆的位置，单位米
            "width": required_width + 0.5,  # 加一些余量
            "depth": required_depth + 0.5,
            "angle": 0.0 if self.mode != ParkingMode.SLANT else 30.0,  # 斜向停车位角度
            "is_suitable": True
        }
        
        logger.info(f"找到合适的停车位: {self.parking_spot}")
        return self.parking_spot
    
    def plan_path(self):
        """规划泊车路径"""
        if not self.parking_spot or not self.current_position:
            logger.error("未找到停车位或当前位置未设置，无法规划路径")
            return []
        
        logger.info("开始规划泊车路径")
        self.stage = ParkingStage.MANEUVERING
        
        # 实际应用中，应该基于车辆几何、运动学模型等规划详细路径
        # 以下是模拟路径
        
        if self.mode == ParkingMode.PERPENDICULAR:
            # 垂直泊车路径（简化）
            self.path = [
                {"position": [5.0, 2.0], "steering": 0, "gear": "D", "speed": 3},
                {"position": [7.0, 2.0], "steering": -30, "gear": "D", "speed": 2},
                {"position": [8.5, 0.5], "steering": -30, "gear": "D", "speed": 1},
                {"position": [10.0, 0.0], "steering": 0, "gear": "D", "speed": 1}
            ]
        elif self.mode == ParkingMode.PARALLEL:
            # 平行泊车路径（简化）
            self.path = [
                {"position": [12.0, 2.0], "steering": 0, "gear": "D", "speed": 3},
                {"position": [10.0, 2.0], "steering": 0, "gear": "D", "speed": 2},
                {"position": [10.0, 2.0], "steering": -30, "gear": "R", "speed": 2},
                {"position": [8.0, 0.5], "steering": 30, "gear": "R", "speed": 1},
                {"position": [7.0, 0.0], "steering": 0, "gear": "D", "speed": 1}
            ]
        else:  # SLANT
            # 斜向泊车路径（简化）
            self.path = [
                {"position": [6.0, 3.0], "steering": 0, "gear": "D", "speed": 3},
                {"position": [8.0, 3.0], "steering": -20, "gear": "D", "speed": 2},
                {"position": [9.5, 1.5], "steering": -20, "gear": "D", "speed": 1},
                {"position": [10.0, 0.3], "steering": 0, "gear": "D", "speed": 1}
            ]
        
        logger.info(f"路径规划完成，共 {len(self.path)} 个路径点")
        return self.path
    
    def execute_parking(self):
        """执行泊车过程"""
        if not self.path:
            logger.error("未规划路径，无法执行泊车")
            self.stage = ParkingStage.FAILED
            return False
        
        logger.info("开始执行泊车操作")
        
        for idx, waypoint in enumerate(self.path):
            logger.info(f"执行路径点 {idx+1}/{len(self.path)}: {waypoint}")
            
            # 这里应该是实际控制车辆的代码
            # 模拟泊车过程
            time.sleep(0.5)  # 模拟操作时间
            
            # 更新当前位置（假设）
            self.current_position = waypoint["position"]
        
        # 完成入位
        self.stage = ParkingStage.ADJUSTING
        
        # 执行微调
        logger.info("执行泊车位置微调")
        time.sleep(1.0)  # 模拟微调时间
        
        self.stage = ParkingStage.COMPLETED
        logger.info("泊车完成")
        
        return True
    
    def abort_parking(self):
        """中止泊车过程"""
        logger.info("中止泊车操作")
        self.stage = ParkingStage.FAILED
        return True
    
    def get_status(self):
        """获取当前泊车状态"""
        return {
            "mode": self.mode.name if self.mode else "未设置",
            "stage": self.stage.name,
            "current_position": self.current_position,
            "parking_spot": self.parking_spot,
            "path_points": len(self.path) if self.path else 0
        }
    
    def parking_completed(self):
        """检查泊车是否完成"""
        return self.stage == ParkingStage.COMPLETED
    
    def parking_failed(self):
        """检查泊车是否失败"""
        return self.stage == ParkingStage.FAILED 