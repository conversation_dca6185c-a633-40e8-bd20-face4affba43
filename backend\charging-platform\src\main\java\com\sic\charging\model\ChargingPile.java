package com.sic.charging.model;

import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;

@Entity
@Table(name = "charging_piles")
@Data
public class ChargingPile {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true)
    private String pileCode;

    @Column(nullable = false)
    private String pileType; // 快充/慢充

    @Column(nullable = false)
    private Integer powerRating; // 功率，单位kW

    @Column(nullable = false)
    private String connectorType; // 接口类型 (GB/T, CCS, CHAdeMO, etc.)

    @Column(nullable = false)
    private BigDecimal pricePerKwh; // 每度电价格

    @Column(nullable = false)
    private String status; // AVAILABLE, CHARGING, OFFLINE, MAINTENANCE

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "charging_station_id", nullable = false)
    private ChargingStation chargingStation;
} 