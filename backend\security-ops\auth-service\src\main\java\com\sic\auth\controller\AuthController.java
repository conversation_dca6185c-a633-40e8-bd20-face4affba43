package com.sic.auth.controller;

import com.sic.auth.model.User;
import com.sic.auth.repository.UserRepository;
import com.sic.auth.security.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/auth")
public class AuthController {
    @Autowired
    private AuthenticationManager authenticationManager;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Autowired
    private JwtUtil jwtUtil;

    @PostMapping("/register")
    public Map<String, Object> register(@RequestBody User user) {
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        userRepository.save(user);
        Map<String, Object> res = new HashMap<>();
        res.put("msg", "注册成功");
        return res;
    }

    @PostMapping("/login")
    public Map<String, Object> login(@RequestBody Map<String, String> req) {
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(req.get("username"), req.get("password")));
        SecurityContextHolder.getContext().setAuthentication(authentication);
        User user = userRepository.findByUsername(req.get("username")).orElseThrow();
        String token = jwtUtil.generateToken(user.getUsername(), user.getRole());
        Map<String, Object> res = new HashMap<>();
        res.put("token", token);
        res.put("role", user.getRole());
        return res;
    }

    @GetMapping("/me")
    public User me() {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        return userRepository.findByUsername(username).orElseThrow();
    }
} 