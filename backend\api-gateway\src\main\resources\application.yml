server:
  port: 8080

spring:
  application:
    name: api-gateway
  cloud:
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      routes:
        - id: auth-service
          uri: lb://auth-service
          predicates:
            - Path=/api/auth/**
        - id: battery-service
          uri: lb://battery-service
          predicates:
            - Path=/api/battery/**
        - id: charging-service
          uri: lb://charging-service
          predicates:
            - Path=/api/charging/**
        - id: adas-service
          uri: lb://adas-service
          predicates:
            - Path=/api/adas/**
        - id: tbox-service
          uri: lb://tbox-service
          predicates:
            - Path=/api/tbox/**
        - id: user-service
          uri: lb://user-service
          predicates:
            - Path=/api/user/**
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
  instance:
    prefer-ip-address: true

# 日志配置
logging:
  level:
    root: INFO
    org.springframework.cloud.gateway: DEBUG
    reactor.netty.http.client: DEBUG

# 自定义配置
jwt:
  secret: sic_secret_key_2024 