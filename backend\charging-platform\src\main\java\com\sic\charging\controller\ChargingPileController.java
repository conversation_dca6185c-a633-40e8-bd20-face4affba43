package com.sic.charging.controller;

import com.sic.charging.model.ChargingPile;
import com.sic.charging.service.ChargingPileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/charging/piles")
@Slf4j
public class ChargingPileController {

    @Autowired
    private ChargingPileService chargingPileService;

    @PostMapping
    public ResponseEntity<ChargingPile> createPile(@Valid @RequestBody ChargingPile pile) {
        ChargingPile createdPile = chargingPileService.createPile(pile);
        return new ResponseEntity<>(createdPile, HttpStatus.CREATED);
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getPileById(@PathVariable Long id) {
        return chargingPileService.getPileById(id)
                .map(pile -> ResponseEntity.ok(pile))
                .orElseGet(() -> {
                    Map<String, String> response = new HashMap<>();
                    response.put("message", "充电桩不存在");
                    return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
                });
    }

    @GetMapping("/code/{pileCode}")
    public ResponseEntity<?> getPileByCode(@PathVariable String pileCode) {
        return chargingPileService.getPileByCode(pileCode)
                .map(pile -> ResponseEntity.ok(pile))
                .orElseGet(() -> {
                    Map<String, String> response = new HashMap<>();
                    response.put("message", "充电桩不存在");
                    return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
                });
    }

    @GetMapping
    public ResponseEntity<List<ChargingPile>> getAllPiles() {
        List<ChargingPile> piles = chargingPileService.getAllPiles();
        return ResponseEntity.ok(piles);
    }

    @GetMapping("/station/{stationId}")
    public ResponseEntity<List<ChargingPile>> getPilesByStationId(@PathVariable Long stationId) {
        List<ChargingPile> piles = chargingPileService.getPilesByStationId(stationId);
        return ResponseEntity.ok(piles);
    }

    @GetMapping("/station/{stationId}/available")
    public ResponseEntity<List<ChargingPile>> getAvailablePilesByStationId(@PathVariable Long stationId) {
        List<ChargingPile> piles = chargingPileService.getAvailablePilesByStationId(stationId);
        return ResponseEntity.ok(piles);
    }

    @PutMapping("/{id}")
    public ResponseEntity<?> updatePile(@PathVariable Long id, @Valid @RequestBody ChargingPile pile) {
        try {
            ChargingPile updatedPile = chargingPileService.updatePile(id, pile);
            return ResponseEntity.ok(updatedPile);
        } catch (RuntimeException e) {
            Map<String, String> response = new HashMap<>();
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<?> deletePile(@PathVariable Long id) {
        try {
            chargingPileService.deletePile(id);
            Map<String, String> response = new HashMap<>();
            response.put("message", "充电桩已删除");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> response = new HashMap<>();
            response.put("message", "删除充电桩失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @PatchMapping("/{id}/status")
    public ResponseEntity<?> updatePileStatus(@PathVariable Long id, @RequestParam String status) {
        try {
            chargingPileService.updatePileStatus(id, status);
            Map<String, String> response = new HashMap<>();
            response.put("message", "充电桩状态已更新");
            return ResponseEntity.ok(response);
        } catch (RuntimeException e) {
            Map<String, String> response = new HashMap<>();
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }
    }
} 