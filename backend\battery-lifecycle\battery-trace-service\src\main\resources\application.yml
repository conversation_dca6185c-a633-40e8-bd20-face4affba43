server:
  port: 8082

spring:
  application:
    name: battery-trace-service
  datasource:
    url: ****************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
  instance:
    prefer-ip-address: true

# 区块链配置
fabric:
  network-config: classpath:fabric-network-config.json
  channel-name: battery-channel
  chaincode-name: battery-trace
  wallet-path: wallet
  user-name: admin

# 日志配置
logging:
  level:
    root: INFO
    com.sic.battery: DEBUG 