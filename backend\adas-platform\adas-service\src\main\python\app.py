from flask import Flask, request, jsonify
import logging
import time
import json
import threading
import numpy as np
import cv2
from environment_perception import EnvironmentPerception
from active_warning import ActiveWarning
from smart_parking.parking_routes import register_parking_routes

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 初始化Flask应用
app = Flask(__name__)

# 注册智能泊车路由
register_parking_routes(app)

# 初始化ADAS模块
perception = EnvironmentPerception()
warning = ActiveWarning()

# 模拟数据
simulation_data = {
    "vehicle_speed": 60,
    "steering_angle": 0,
    "front_distance": 25.0,
    "relative_speed": 10.0,
    "lane_position": 0.1,
    "eye_closed_time": 0.2,
    "head_position_deviation": 0.1,
    "speed_limit": 80
}

# 全局变量用于线程间共享
global_frame = None
global_analysis_results = {}
running = False

# 用于状态同步的线程锁
status_lock = threading.Lock()

@app.route('/api/adas/status', methods=['GET'])
def get_status():
    """获取ADAS系统状态"""
    with status_lock:
        status = {
            "system": "ADAS Platform",
            "camera_connected": perception.camera is not None,
            "camera_calibrated": perception.camera_calibrated,
            "lidar_calibrated": perception.lidar_calibrated,
            "timestamp": time.time()
        }
    return jsonify(status)

@app.route('/api/adas/perception/initialize', methods=['POST'])
def initialize_perception():
    """初始化感知系统"""
    try:
        data = request.json or {}
        camera_id = data.get('camera_id', 0)
        lidar_port = data.get('lidar_port', '/dev/ttyUSB0')
        
        success = perception.initialize_sensors(camera_id, lidar_port)
        
        if success:
            return jsonify({
                "status": "success",
                "message": "感知系统初始化成功"
            })
        else:
            return jsonify({
                "status": "error",
                "message": "感知系统初始化失败"
            }), 500
    except Exception as e:
        logger.error(f"初始化感知系统异常: {e}")
        return jsonify({
            "status": "error",
            "message": f"初始化异常: {str(e)}"
        }), 500

@app.route('/api/adas/perception/frame', methods=['GET'])
def get_latest_frame():
    """获取最新处理的帧"""
    global global_analysis_results
    
    with status_lock:
        if global_analysis_results and "lane_detection" in global_analysis_results:
            # 将OpenCV图像编码为JPEG
            _, img_encoded = cv2.imencode('.jpg', global_analysis_results["lane_detection"])
            response = app.response_class(
                img_encoded.tobytes(),
                mimetype='image/jpeg'
            )
            return response
        else:
            return jsonify({
                "status": "error",
                "message": "无可用的处理帧"
            }), 404

@app.route('/api/adas/warning/check', methods=['POST'])
def check_warnings():
    """根据传感器数据检查预警"""
    try:
        data = request.json or simulation_data
        warnings = warning.check_all_warnings(data)
        
        return jsonify({
            "status": "success",
            "timestamp": time.time(),
            "warnings": warnings,
            "vehicle_data": {
                "speed": data.get("vehicle_speed", 0),
                "steering": data.get("steering_angle", 0)
            }
        })
    except Exception as e:
        logger.error(f"检查预警异常: {e}")
        return jsonify({
            "status": "error",
            "message": f"检查预警异常: {str(e)}"
        }), 500

@app.route('/api/adas/perception/obstacles', methods=['GET'])
def get_obstacles():
    """获取当前检测到的障碍物"""
    global global_analysis_results
    global global_frame
    
    with status_lock:
        if global_frame is None:
            return jsonify({
                "status": "error",
                "message": "无可用的摄像头帧"
            }), 404
        
        try:
            obstacles = perception.detect_obstacles(global_frame)
            return jsonify({
                "status": "success",
                "timestamp": time.time(),
                "obstacles_count": len(obstacles),
                "obstacles": obstacles
            })
        except Exception as e:
            logger.error(f"检测障碍物异常: {e}")
            return jsonify({
                "status": "error",
                "message": f"障碍物检测异常: {str(e)}"
            }), 500

@app.route('/api/adas/traffic_light', methods=['GET'])
def get_traffic_light():
    """获取交通信号灯状态"""
    global global_frame
    
    with status_lock:
        if global_frame is None:
            return jsonify({
                "status": "error",
                "message": "无可用的摄像头帧"
            }), 404
        
        try:
            light_status = perception.analyze_traffic_light(global_frame)
            return jsonify({
                "status": "success",
                "timestamp": time.time(),
                "traffic_light": light_status
            })
        except Exception as e:
            logger.error(f"分析交通信号灯异常: {e}")
            return jsonify({
                "status": "error",
                "message": f"交通信号灯分析异常: {str(e)}"
            }), 500

@app.route('/api/adas/simulation/data', methods=['POST'])
def update_simulation_data():
    """更新模拟数据"""
    global simulation_data
    
    try:
        data = request.json
        if data:
            simulation_data.update(data)
            return jsonify({
                "status": "success",
                "message": "模拟数据已更新",
                "current_data": simulation_data
            })
        else:
            return jsonify({
                "status": "error",
                "message": "未提供数据"
            }), 400
    except Exception as e:
        logger.error(f"更新模拟数据异常: {e}")
        return jsonify({
            "status": "error",
            "message": f"更新异常: {str(e)}"
        }), 500

def processing_thread():
    """后台处理线程，持续处理视频帧"""
    global global_frame
    global global_analysis_results
    global running
    
    logger.info("启动视频处理线程")
    
    while running:
        if perception.camera is not None:
            try:
                results = perception.process_camera_frame()
                
                if results is not None:
                    with status_lock:
                        global_frame = results["original"].copy()
                        global_analysis_results = results
            except Exception as e:
                logger.error(f"视频处理异常: {e}")
            
        # 降低CPU使用率
        time.sleep(0.03)  # 约30帧/秒
    
    logger.info("视频处理线程已停止")

@app.route('/api/adas/control/start', methods=['POST'])
def start_processing():
    """启动视频处理"""
    global running
    
    if running:
        return jsonify({
            "status": "warning",
            "message": "处理已经在运行中"
        })
    
    with status_lock:
        running = True
        thread = threading.Thread(target=processing_thread)
        thread.daemon = True
        thread.start()
    
    return jsonify({
        "status": "success",
        "message": "视频处理已启动"
    })

@app.route('/api/adas/control/stop', methods=['POST'])
def stop_processing():
    """停止视频处理"""
    global running
    
    with status_lock:
        running = False
        if perception.camera is not None:
            perception.release_resources()
    
    return jsonify({
        "status": "success",
        "message": "视频处理已停止"
    })

if __name__ == '__main__':
    # 设置服务器
    app.run(host='0.0.0.0', port=5000, debug=False, threaded=True) 