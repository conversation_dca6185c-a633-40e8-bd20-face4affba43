package com.sic.tbox.controller;

import com.sic.tbox.model.Vehicle;
import com.sic.tbox.service.VehicleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.stereotype.Controller;

@Controller
public class VehicleWebSocketController {
    
    @Autowired
    private VehicleService vehicleService;
    
    @MessageMapping("/vehicle/location")
    @SendTo("/topic/vehicle/location")
    public Vehicle updateLocation(Vehicle vehicle) {
        return vehicleService.updateLocation(vehicle);
    }
    
    @MessageMapping("/vehicle/diagnostics")
    @SendTo("/topic/vehicle/diagnostics")
    public Vehicle updateDiagnostics(Vehicle vehicle) {
        return vehicleService.updateDiagnostics(vehicle);
    }
    
    @MessageMapping("/vehicle/control")
    @SendTo("/topic/vehicle/control")
    public Vehicle controlVehicle(Vehicle vehicle) {
        return vehicleService.controlVehicle(vehicle);
    }
} 