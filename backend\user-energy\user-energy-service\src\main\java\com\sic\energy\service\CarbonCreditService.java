package com.sic.energy.service;

import com.sic.energy.model.CarbonCredit;
import java.util.List;
import java.util.Map;

public interface CarbonCreditService {
    
    List<CarbonCredit> getUserCarbonCredits(String userId);
    
    CarbonCredit addCarbonCredits(CarbonCredit carbonCredit);
    
    Double getTotalCarbonCredits(String userId);
    
    Map<String, Object> getUserCarbonCreditsSummary(String userId);
    
    boolean redeemCredits(String userId, Double credits, String purpose);
} 