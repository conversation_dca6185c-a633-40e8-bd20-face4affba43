package com.sic.charging.repository;

import com.sic.charging.model.ChargingPile;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ChargingPileRepository extends JpaRepository<ChargingPile, Long> {
    
    List<ChargingPile> findByChargingStationId(Long stationId);
    
    List<ChargingPile> findByStatus(String status);
    
    Optional<ChargingPile> findByPileCode(String pileCode);
    
    @Query("SELECT cp FROM ChargingPile cp WHERE cp.chargingStation.id = :stationId AND cp.status = 'AVAILABLE'")
    List<ChargingPile> findAvailablePilesByStationId(@Param("stationId") Long stationId);
    
    @Query("SELECT cp FROM ChargingPile cp WHERE cp.pileType = :pileType AND cp.status = 'AVAILABLE'")
    List<ChargingPile> findAvailablePilesByType(@Param("pileType") String pileType);
} 