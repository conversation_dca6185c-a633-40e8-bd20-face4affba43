# 充电平台微服务

## 概述
充电平台微服务负责电动汽车充电基础设施的管理，包括充电站、充电桩、充电记录和预约管理等功能。

## 技术栈
- Spring Boot 2.7.8
- Spring Cloud (Eureka Client)
- Spring Data JPA
- MySQL
- RESTful API

## 功能模块
1. **充电站管理**
   - 充电站CRUD
   - 地理位置查询
   - 可用性状态管理
   
2. **充电桩管理**
   - 充电桩CRUD
   - 状态监控
   - 类型和规格管理
   
3. **充电记录**
   - 开始/结束充电
   - 电量统计
   - 费用计算
   - 支付状态管理
   
4. **预约管理**
   - 创建/取消预约
   - 时间冲突检测
   - 预约状态跟踪

## API接口
- `/api/charging/stations` - 充电站相关接口
- `/api/charging/piles` - 充电桩相关接口
- `/api/charging/records` - 充电记录相关接口
- `/api/charging/reservations` - 预约相关接口

## 数据库设计
- `charging_stations` - 充电站信息
- `charging_piles` - 充电桩信息
- `charging_records` - 充电记录
- `reservations` - 预约信息

## 运行说明
1. 确保MySQL数据库已启动
2. 确保Eureka服务已启动
3. 执行 `mvn spring-boot:run` 启动服务

## 后续开发计划
1. 完成充电记录和预约服务实现
2. 添加实时监控功能
3. 集成支付系统
4. 开发负载均衡和智能调度算法
5. 添加用户评价和反馈功能 