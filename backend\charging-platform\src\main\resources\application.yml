server:
  port: 8083

spring:
  application:
    name: charging-platform-service
  datasource:
    url: ********************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
  instance:
    prefer-ip-address: true

# 日志配置
logging:
  level:
    root: INFO
    com.sic.charging: DEBUG 