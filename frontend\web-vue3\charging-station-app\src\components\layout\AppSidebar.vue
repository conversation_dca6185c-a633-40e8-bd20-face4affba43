<template>
  <div class="app-sidebar">
    <el-menu
      :default-active="activeMenu"
      class="sidebar-menu"
      :collapse="isCollapse"
      background-color="#304156"
      text-color="#bfcbd9"
      active-text-color="#409EFF"
      router
    >
      <el-menu-item index="/dashboard">
        <el-icon><el-icon-menu /></el-icon>
        <template #title>首页</template>
      </el-menu-item>
      
      <el-sub-menu index="1">
        <template #title>
          <el-icon><el-icon-location /></el-icon>
          <span>充电站管理</span>
        </template>
        <el-menu-item index="/stations/map">地图视图</el-menu-item>
        <el-menu-item index="/stations/list">列表视图</el-menu-item>
        <el-menu-item index="/stations/add">添加充电站</el-menu-item>
      </el-sub-menu>
      
      <el-sub-menu index="2">
        <template #title>
          <el-icon><el-icon-connection /></el-icon>
          <span>充电桩管理</span>
        </template>
        <el-menu-item index="/chargers/list">充电桩列表</el-menu-item>
        <el-menu-item index="/chargers/status">状态监控</el-menu-item>
        <el-menu-item index="/chargers/maintenance">维护管理</el-menu-item>
      </el-sub-menu>
      
      <el-sub-menu index="3">
        <template #title>
          <el-icon><el-icon-document /></el-icon>
          <span>订单管理</span>
        </template>
        <el-menu-item index="/orders/list">订单列表</el-menu-item>
        <el-menu-item index="/orders/statistics">订单统计</el-menu-item>
      </el-sub-menu>
      
      <el-sub-menu index="4">
        <template #title>
          <el-icon><el-icon-user /></el-icon>
          <span>用户管理</span>
        </template>
        <el-menu-item index="/users/list">用户列表</el-menu-item>
        <el-menu-item index="/users/feedback">用户反馈</el-menu-item>
      </el-sub-menu>
      
      <el-menu-item index="/settings">
        <el-icon><el-icon-setting /></el-icon>
        <template #title>系统设置</template>
      </el-menu-item>
    </el-menu>
    
    <div class="collapse-btn" @click="toggleCollapse">
      <el-icon v-if="isCollapse"><el-icon-arrow-right /></el-icon>
      <el-icon v-else><el-icon-arrow-left /></el-icon>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '@/store/app'

export default defineComponent({
  name: 'AppSidebar',
  setup() {
    const route = useRoute()
    const appStore = useAppStore()
    
    // 侧边栏折叠状态
    const isCollapse = computed(() => appStore.sidebarCollapsed)
    
    // 当前激活的菜单
    const activeMenu = computed(() => {
      return route.path
    })
    
    // 切换侧边栏折叠状态
    const toggleCollapse = () => {
      appStore.toggleSidebar()
    }
    
    return {
      isCollapse,
      activeMenu,
      toggleCollapse
    }
  }
})
</script>

<style lang="scss" scoped>
.app-sidebar {
  height: 100%;
  position: relative;
  
  .sidebar-menu {
    height: 100%;
    border-right: none;
  }
  
  .collapse-btn {
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    text-align: center;
    cursor: pointer;
    color: #bfcbd9;
    padding: 10px 0;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}
</style> 