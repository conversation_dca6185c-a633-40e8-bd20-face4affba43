package com.sic.energy.repository;

import com.sic.energy.model.FleetVehicle;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface FleetVehicleRepository extends MongoRepository<FleetVehicle, String> {
    
    List<FleetVehicle> findByFleetId(String fleetId);
    
    Optional<FleetVehicle> findByVin(String vin);
} 