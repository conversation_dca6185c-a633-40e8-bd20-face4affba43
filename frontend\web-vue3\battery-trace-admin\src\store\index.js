import { createStore } from 'vuex'

export default createStore({
  state: {
    token: localStorage.getItem('token') || '',
    user: JSON.parse(localStorage.getItem('user')) || {},
    isCollapse: false
  },
  getters: {
    token: state => state.token,
    user: state => state.user,
    isCollapse: state => state.isCollapse
  },
  mutations: {
    SET_TOKEN(state, token) {
      state.token = token
      localStorage.setItem('token', token)
    },
    SET_USER(state, user) {
      state.user = user
      localStorage.setItem('user', JSON.stringify(user))
    },
    LOGOUT(state) {
      state.token = ''
      state.user = {}
      localStorage.removeItem('token')
      localStorage.removeItem('user')
    },
    TOGGLE_SIDEBAR(state) {
      state.isCollapse = !state.isCollapse
    }
  },
  actions: {
    login({ commit }, { token, user }) {
      commit('SET_TOKEN', token)
      commit('SET_USER', user)
    },
    logout({ commit }) {
      commit('LOGOUT')
    },
    toggleSidebar({ commit }) {
      commit('TOGGLE_SIDEBAR')
    }
  },
  modules: {
  }
}) 