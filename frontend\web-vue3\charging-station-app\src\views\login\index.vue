<template>
  <div class="login-container">
    <el-card class="login-card">
      <div class="login-logo">
        <img src="@/assets/images/logo.png" alt="Logo" class="logo-image" />
        <h2 class="app-title">充电站管理系统</h2>
      </div>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @keyup.enter="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="用户名"
            prefix-icon="el-icon-user"
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="密码"
            prefix-icon="el-icon-lock"
            show-password
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            :loading="loading"
            type="primary"
            class="login-button"
            @click="handleLogin"
          >
            登录
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/user'

export default defineComponent({
  name: 'Login',
  setup() {
    const router = useRouter()
    const userStore = useUserStore()
    const loginFormRef = ref()
    
    const state = reactive({
      loginForm: {
        username: '',
        password: ''
      },
      loginRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ]
      },
      loading: false
    })
    
    const handleLogin = async () => {
      if (!loginFormRef.value) return
      
      try {
        await loginFormRef.value.validate()
        
        state.loading = true
        try {
          await userStore.login(state.loginForm.username, state.loginForm.password)
          ElMessage.success('登录成功')
          router.push('/')
        } catch (error) {
          ElMessage.error('登录失败：' + (error instanceof Error ? error.message : '未知错误'))
        } finally {
          state.loading = false
        }
      } catch (error) {
        console.error('表单验证失败', error)
      }
    }
    
    return {
      ...toRefs(state),
      loginFormRef,
      handleLogin
    }
  }
})
</script>

<style lang="scss" scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f7fa;
  
  .login-card {
    width: 400px;
    border-radius: 8px;
    
    .login-logo {
      text-align: center;
      margin-bottom: 30px;
      
      .logo-image {
        width: 80px;
        height: 80px;
        margin-bottom: 10px;
      }
      
      .app-title {
        font-size: 24px;
        color: #303133;
        margin: 0;
      }
    }
    
    .login-form {
      .login-button {
        width: 100%;
      }
    }
  }
}
</style> 