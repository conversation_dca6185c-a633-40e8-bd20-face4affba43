package com.sic.battery.trace.model;

import lombok.Data;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "batteries")
@Data
public class Battery {
    @Id
    @Column(nullable = false, unique = true)
    private String batteryId; // 电池唯一ID

    @Column(nullable = false)
    private String batchNumber; // 批次号

    @Column(nullable = false)
    private String manufacturer; // 制造商

    @Column(nullable = false)
    private LocalDateTime productionDate; // 生产日期

    @Column(nullable = false)
    private String model; // 型号

    @Column(nullable = false)
    private Double capacity; // 容量(Ah)

    @Column(nullable = false)
    private Double voltage; // 电压(V)

    @Column(nullable = false)
    private Double weight; // 重量(kg)

    @Column(length = 1000)
    private String rawMaterials; // 原材料信息(JSON格式)

    @Column(length = 1000)
    private String productionParams; // 生产工艺参数(JSON格式)

    @Column(length = 1000)
    private String testResults; // 出厂测试结果(JSON格式)

    @Column
    private String blockchainTxId; // 区块链交易ID
} 