package com.sic.battery.trace.repository;

import com.sic.battery.trace.model.Battery;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BatteryRepository extends JpaRepository<Battery, String> {
    List<Battery> findByBatchNumber(String batchNumber);
    List<Battery> findByManufacturer(String manufacturer);
    List<Battery> findByModel(String model);
} 