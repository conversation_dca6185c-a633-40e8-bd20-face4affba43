import numpy as np
import cv2
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnvironmentPerception:
    def __init__(self):
        self.camera = None
        self.lidar = None
        self.camera_calibrated = False
        self.lidar_calibrated = False
        logger.info("初始化环境感知模块")
        
    def initialize_sensors(self, camera_id=0, lidar_port='/dev/ttyUSB0'):
        """初始化摄像头和LiDAR传感器"""
        try:
            # 初始化摄像头
            self.camera = cv2.VideoCapture(camera_id)
            if not self.camera.isOpened():
                logger.error(f"无法打开摄像头 {camera_id}")
                return False
            logger.info(f"摄像头 {camera_id} 初始化成功")
            
            # 模拟连接LiDAR
            logger.info(f"LiDAR设备连接至 {lidar_port}")
            self.lidar_calibrated = True
            self.camera_calibrated = True
            return True
        except Exception as e:
            logger.error(f"传感器初始化失败: {e}")
            return False
            
    def process_camera_frame(self):
        """处理摄像头画面，进行车道线检测等操作"""
        if self.camera is None:
            logger.warning("摄像头未初始化")
            return None
            
        ret, frame = self.camera.read()
        if not ret:
            logger.warning("无法读取摄像头画面")
            return None
            
        # 图像预处理
        height, width = frame.shape[:2]
        
        # 将图像转换为灰度
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # 高斯模糊去噪
        blur = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # Canny边缘检测
        edges = cv2.Canny(blur, 50, 150)
        
        # 定义感兴趣区域 (ROI)，关注道路部分
        mask = np.zeros_like(edges)
        polygon = np.array([[(0, height), (width, height), (width // 2, height // 2)]], np.int32)
        cv2.fillPoly(mask, polygon, 255)
        masked_edges = cv2.bitwise_and(edges, mask)
        
        # 霍夫变换检测直线（车道线）
        lines = cv2.HoughLinesP(masked_edges, 1, np.pi/180, 50, minLineLength=100, maxLineGap=50)
        
        # 检测结果可视化
        line_image = np.zeros_like(frame)
        lane_lines = []
        
        if lines is not None:
            for line in lines:
                x1, y1, x2, y2 = line[0]
                # 计算直线斜率，过滤非车道线
                if x2 - x1 == 0:  # 避免除零错误
                    continue
                slope = (y2 - y1) / (x2 - x1)
                # 仅考虑合适斜率的线（车道线通常有一定的倾斜度）
                if abs(slope) > 0.5:
                    cv2.line(line_image, (x1, y1), (x2, y2), (0, 255, 0), 5)
                    lane_lines.append(line[0])
        
        # 将车道线叠加到原始图像上
        lane_overlay = cv2.addWeighted(frame, 0.8, line_image, 1, 1)
        
        # 返回处理结果
        results = {
            "original": frame,
            "edges": edges,
            "lane_detection": lane_overlay,
            "lane_lines": lane_lines
        }
        
        return results
    
    def detect_obstacles(self, frame):
        """检测画面中的障碍物"""
        obstacles = []
        
        # 这里使用简化的障碍物检测逻辑
        # 实际应用中可以使用目标检测模型如YOLO、SSD等
        
        try:
            # 转换为灰度
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # 二值化
            _, binary = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)
            
            # 轮廓检测
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 处理检测到的轮廓
            for contour in contours:
                # 过滤掉过小的轮廓
                area = cv2.contourArea(contour)
                if area > 1000:  # 面积阈值
                    x, y, w, h = cv2.boundingRect(contour)
                    obstacles.append({
                        "type": "unknown",
                        "position": (x + w//2, y + h//2),  # 中心点位置
                        "size": (w, h),
                        "area": area
                    })
        except Exception as e:
            logger.error(f"障碍物检测错误: {e}")
        
        return obstacles
        
    def estimate_distance(self, object_size, known_width=1.0):
        """估算物体距离（简化版）"""
        # 使用简单的三角测量估算距离
        # 实际应用中应使用相机校准参数和更复杂的计算
        
        # 焦距（需通过相机校准获取）
        focal_length = 800.0
        
        # 通过物体大小估算距离
        distance = (known_width * focal_length) / object_size
        return distance
    
    def analyze_traffic_light(self, frame):
        """交通信号灯识别（简化版）"""
        # 实际应用中应使用更复杂的颜色分析和形状识别
        
        # 转换到HSV色彩空间便于颜色检测
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # 定义红绿灯的HSV范围
        lower_red = np.array([0, 120, 70])
        upper_red = np.array([10, 255, 255])
        lower_green = np.array([35, 100, 100])
        upper_green = np.array([85, 255, 255])
        
        # 颜色掩码
        red_mask = cv2.inRange(hsv, lower_red, upper_red)
        green_mask = cv2.inRange(hsv, lower_green, upper_green)
        
        # 计算每个颜色的像素数量
        red_pixels = cv2.countNonZero(red_mask)
        green_pixels = cv2.countNonZero(green_mask)
        
        # 判断信号灯状态
        if red_pixels > 500:  # 像素阈值
            return "RED"
        elif green_pixels > 500:
            return "GREEN"
        else:
            return "UNKNOWN"
    
    def release_resources(self):
        """释放资源"""
        if self.camera is not None:
            self.camera.release()
            logger.info("已释放摄像头资源")
        
        # 关闭其他资源（如LiDAR）
        logger.info("已释放所有感知模块资源") 