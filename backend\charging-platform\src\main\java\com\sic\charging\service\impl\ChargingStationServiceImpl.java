package com.sic.charging.service.impl;

import com.sic.charging.model.ChargingStation;
import com.sic.charging.repository.ChargingStationRepository;
import com.sic.charging.service.ChargingStationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class ChargingStationServiceImpl implements ChargingStationService {

    @Autowired
    private ChargingStationRepository chargingStationRepository;

    @Override
    @Transactional
    public ChargingStation createStation(ChargingStation station) {
        log.info("创建充电站: {}", station.getName());
        return chargingStationRepository.save(station);
    }

    @Override
    public Optional<ChargingStation> getStationById(Long id) {
        log.debug("获取充电站信息, id: {}", id);
        return chargingStationRepository.findById(id);
    }

    @Override
    public List<ChargingStation> getAllStations() {
        log.debug("获取所有充电站");
        return chargingStationRepository.findAll();
    }

    @Override
    public List<ChargingStation> getAvailableStations() {
        log.debug("获取所有可用充电站");
        return chargingStationRepository.findAvailableStations();
    }

    @Override
    public List<ChargingStation> getNearbyStations(BigDecimal latitude, BigDecimal longitude, double radiusInKm) {
        log.debug("获取附近充电站, 纬度: {}, 经度: {}, 半径: {}km", latitude, longitude, radiusInKm);
        // 将公里转换为米
        double radiusMeters = radiusInKm * 1000;
        return chargingStationRepository.findNearbyStations(latitude, longitude, radiusMeters);
    }

    @Override
    @Transactional
    public ChargingStation updateStation(Long id, ChargingStation station) {
        log.info("更新充电站信息, id: {}", id);
        Optional<ChargingStation> existingStationOpt = chargingStationRepository.findById(id);
        if (existingStationOpt.isPresent()) {
            ChargingStation existingStation = existingStationOpt.get();
            existingStation.setName(station.getName());
            existingStation.setAddress(station.getAddress());
            existingStation.setLatitude(station.getLatitude());
            existingStation.setLongitude(station.getLongitude());
            existingStation.setOperatorName(station.getOperatorName());
            existingStation.setContactPhone(station.getContactPhone());
            existingStation.setDescription(station.getDescription());
            existingStation.setIsOpen(station.getIsOpen());
            return chargingStationRepository.save(existingStation);
        } else {
            log.error("充电站不存在, id: {}", id);
            throw new RuntimeException("充电站不存在");
        }
    }

    @Override
    @Transactional
    public void deleteStation(Long id) {
        log.info("删除充电站, id: {}", id);
        chargingStationRepository.deleteById(id);
    }

    @Override
    @Transactional
    public void updateStationAvailability(Long stationId, int availableChargers, boolean isOpen) {
        log.info("更新充电站可用性, id: {}, 可用充电桩: {}, 是否开放: {}", stationId, availableChargers, isOpen);
        Optional<ChargingStation> stationOpt = chargingStationRepository.findById(stationId);
        if (stationOpt.isPresent()) {
            ChargingStation station = stationOpt.get();
            station.setAvailableChargers(availableChargers);
            station.setIsOpen(isOpen);
            chargingStationRepository.save(station);
        } else {
            log.error("充电站不存在, id: {}", stationId);
            throw new RuntimeException("充电站不存在");
        }
    }
} 