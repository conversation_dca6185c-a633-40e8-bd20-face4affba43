apiVersion: apps/v1
kind: Deployment
metadata:
  name: battery-trace-service
  labels:
    app: battery-trace-service
    tier: backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: battery-trace-service
  template:
    metadata:
      labels:
        app: battery-trace-service
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/path: "/actuator/prometheus"
        prometheus.io/port: "8081"
    spec:
      containers:
      - name: battery-trace-service
        image: sic/battery-trace-service:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8081
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE
          value: "http://eureka-server:8761/eureka/"
        - name: SPRING_DATASOURCE_URL
          value: "******************************************************************"
        - name: SPRING_DATASOURCE_USERNAME
          valueFrom:
            secretKeyRef:
              name: mysql-credentials
              key: username
        - name: SPRING_DATASOURCE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mysql-credentials
              key: password
        resources:
          limits:
            cpu: "1"
            memory: "1Gi"
          requests:
            cpu: "500m"
            memory: "512Mi"
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8081
          initialDelaySeconds: 60
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8081
          initialDelaySeconds: 120
          periodSeconds: 30
        volumeMounts:
        - name: fabric-config
          mountPath: /app/fabric-network-config.json
          subPath: fabric-network-config.json
      volumes:
      - name: fabric-config
        configMap:
          name: fabric-network-config 