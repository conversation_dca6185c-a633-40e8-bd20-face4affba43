package com.sic.charging.service;

import com.sic.charging.model.ChargingStation;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

public interface ChargingStationService {
    
    ChargingStation createStation(ChargingStation station);
    
    Optional<ChargingStation> getStationById(Long id);
    
    List<ChargingStation> getAllStations();
    
    List<ChargingStation> getAvailableStations();
    
    List<ChargingStation> getNearbyStations(BigDecimal latitude, BigDecimal longitude, double radiusInKm);
    
    ChargingStation updateStation(Long id, ChargingStation station);
    
    void deleteStation(Long id);
    
    void updateStationAvailability(Long stationId, int availableChargers, boolean isOpen);
} 