<template>
  <div class="app-header">
    <div class="logo-container">
      <img src="@/assets/images/logo.png" alt="充电站管理系统" class="logo" />
      <h1 class="title">充电站管理系统</h1>
    </div>
    <div class="right-menu">
      <el-dropdown trigger="click">
        <div class="user-info">
          <el-avatar :size="32" icon="el-icon-user" />
          <span class="username">{{ username }}</span>
          <i class="el-icon-arrow-down"></i>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="toUserProfile">个人中心</el-dropdown-item>
            <el-dropdown-item @click="toSettings">系统设置</el-dropdown-item>
            <el-dropdown-item divided @click="logout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { useUserStore } from '@/store/user'

export default defineComponent({
  name: 'AppHeader',
  setup() {
    const router = useRouter()
    const userStore = useUserStore()
    const username = ref('管理员')

    // 跳转到个人中心
    const toUserProfile = () => {
      router.push('/profile')
    }

    // 跳转到系统设置
    const toSettings = () => {
      router.push('/settings')
    }

    // 退出登录
    const logout = () => {
      ElMessageBox.confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        userStore.logout()
        router.push('/login')
      }).catch(() => {})
    }

    return {
      username,
      toUserProfile,
      toSettings,
      logout
    }
  }
})
</script>

<style lang="scss" scoped>
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 20px;
  
  .logo-container {
    display: flex;
    align-items: center;
    
    .logo {
      height: 40px;
      margin-right: 10px;
    }
    
    .title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      margin: 0;
    }
  }
  
  .right-menu {
    display: flex;
    align-items: center;
    
    .user-info {
      display: flex;
      align-items: center;
      cursor: pointer;
      
      .username {
        margin: 0 5px;
        font-size: 14px;
      }
    }
  }
}
</style> 