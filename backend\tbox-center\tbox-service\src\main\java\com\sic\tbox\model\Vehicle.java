package com.sic.tbox.model;

import lombok.Data;
import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "vehicles")
@Data
public class Vehicle {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String vin;
    
    @Column(nullable = false)
    private String model;
    
    @Column
    private String batteryId;
    
    @Column
    private boolean online;
    
    @Column
    private Double latitude;
    
    @Column
    private Double longitude;
    
    @Column
    private Integer batterySoc;
    
    @Column
    private String diagnosticData;
    
    @Column
    private LocalDateTime lastUpdateTime;
} 