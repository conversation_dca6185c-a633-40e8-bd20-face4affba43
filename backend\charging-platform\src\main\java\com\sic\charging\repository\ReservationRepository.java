package com.sic.charging.repository;

import com.sic.charging.model.Reservation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ReservationRepository extends JpaRepository<Reservation, Long> {
    
    List<Reservation> findByUserId(String userId);
    
    List<Reservation> findByChargingStationId(Long stationId);
    
    List<Reservation> findByChargingPileId(Long pileId);
    
    List<Reservation> findByStatus(String status);
    
    @Query("SELECT r FROM Reservation r WHERE r.userId = :userId AND r.status IN ('PENDING', 'CONFIRMED')")
    List<Reservation> findActiveReservationsByUserId(@Param("userId") String userId);
    
    @Query("SELECT r FROM Reservation r WHERE r.chargingPile.id = :pileId AND r.status = 'CONFIRMED' " +
           "AND ((r.reservationTime <= :endTime AND r.reservationTime >= :startTime) OR " +
           "(r.reservationTime <= :startTime AND DATEADD(MINUTE, r.durationMinutes, r.reservationTime) >= :startTime))")
    List<Reservation> findConflictingReservations(@Param("pileId") Long pileId,
                                                 @Param("startTime") LocalDateTime startTime,
                                                 @Param("endTime") LocalDateTime endTime);
    
    @Query("SELECT r FROM Reservation r WHERE r.status = 'CONFIRMED' AND r.reservationTime <= :now " +
           "AND DATEADD(MINUTE, r.durationMinutes, r.reservationTime) >= :now")
    List<Reservation> findCurrentActiveReservations(@Param("now") LocalDateTime now);
} 