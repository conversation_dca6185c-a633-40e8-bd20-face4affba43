package com.sic.charging.service;

import com.sic.charging.model.Reservation;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface ReservationService {
    
    Reservation createReservation(Reservation reservation);
    
    Optional<Reservation> getReservationById(Long id);
    
    List<Reservation> getReservationsByUserId(String userId);
    
    List<Reservation> getReservationsByStationId(Long stationId);
    
    List<Reservation> getActiveReservationsByUserId(String userId);
    
    Reservation confirmReservation(Long reservationId);
    
    Reservation cancelReservation(Long reservationId, String reason);
    
    Reservation completeReservation(Long reservationId, LocalDateTime actualArrivalTime);
    
    boolean checkReservationAvailability(Long pileId, LocalDateTime startTime, Integer durationMinutes);
    
    List<Reservation> getCurrentActiveReservations();
} 