package com.sic.battery.trace.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sic.battery.trace.model.Battery;
import com.sic.battery.trace.repository.BatteryRepository;
import com.sic.battery.trace.service.BatteryTraceService;
import lombok.extern.slf4j.Slf4j;
import org.hyperledger.fabric.gateway.Contract;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@Slf4j
public class BatteryTraceServiceImpl implements BatteryTraceService {

    @Autowired
    private BatteryRepository batteryRepository;

    @Autowired
    private Contract fabricContract;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    @Transactional
    public Battery createBattery(Battery battery) {
        try {
            // 生成唯一ID（如果未提供）
            if (battery.getBatteryId() == null || battery.getBatteryId().isEmpty()) {
                battery.setBatteryId(UUID.randomUUID().toString());
            }

            // 将电池信息保存到区块链
            String batteryJson = objectMapper.writeValueAsString(battery);
            byte[] result = fabricContract.submitTransaction("createBattery", battery.getBatteryId(), batteryJson);
            String txId = new String(result, StandardCharsets.UTF_8);
            
            // 设置区块链交易ID
            battery.setBlockchainTxId(txId);
            
            // 保存到数据库
            return batteryRepository.save(battery);
        } catch (Exception e) {
            log.error("创建电池记录失败", e);
            throw new RuntimeException("创建电池记录失败: " + e.getMessage());
        }
    }

    @Override
    public Optional<Battery> getBatteryById(String batteryId) {
        return batteryRepository.findById(batteryId);
    }

    @Override
    public List<Battery> getBatteriesByBatchNumber(String batchNumber) {
        return batteryRepository.findByBatchNumber(batchNumber);
    }

    @Override
    public List<Battery> getBatteriesByManufacturer(String manufacturer) {
        return batteryRepository.findByManufacturer(manufacturer);
    }

    @Override
    public List<Battery> getBatteriesByModel(String model) {
        return batteryRepository.findByModel(model);
    }

    @Override
    public boolean verifyBatteryOnChain(String batteryId) {
        try {
            // 从数据库获取电池信息
            Optional<Battery> batteryOpt = batteryRepository.findById(batteryId);
            if (batteryOpt.isEmpty()) {
                return false;
            }
            
            Battery battery = batteryOpt.get();
            
            // 从区块链获取电池信息
            byte[] result = fabricContract.evaluateTransaction("getBattery", batteryId);
            String onChainBatteryJson = new String(result, StandardCharsets.UTF_8);
            
            // 比较数据库和区块链的信息是否一致
            Battery onChainBattery = objectMapper.readValue(onChainBatteryJson, Battery.class);
            
            // 简单比较，实际应用中可能需要更复杂的比较逻辑
            return battery.getBatteryId().equals(onChainBattery.getBatteryId()) &&
                   battery.getBatchNumber().equals(onChainBattery.getBatchNumber()) &&
                   battery.getManufacturer().equals(onChainBattery.getManufacturer()) &&
                   battery.getModel().equals(onChainBattery.getModel());
        } catch (Exception e) {
            log.error("验证电池溯源信息失败", e);
            return false;
        }
    }
} 