apiVersion: apps/v1
kind: Deployment
metadata:
  name: charging-platform
  labels:
    app: charging-platform
    tier: backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: charging-platform
  template:
    metadata:
      labels:
        app: charging-platform
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/path: "/actuator/prometheus"
        prometheus.io/port: "8082"
    spec:
      containers:
      - name: charging-platform
        image: sic/charging-platform:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8082
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE
          value: "http://eureka-server:8761/eureka/"
        - name: SPRING_DATASOURCE_URL
          value: "*******************************************************************"
        - name: SPRING_DATASOURCE_USERNAME
          valueFrom:
            secretKeyRef:
              name: mysql-credentials
              key: username
        - name: SPRING_DATASOURCE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mysql-credentials
              key: password
        resources:
          limits:
            cpu: "1"
            memory: "1Gi"
          requests:
            cpu: "500m"
            memory: "512Mi"
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8082
          initialDelaySeconds: 60
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8082
          initialDelaySeconds: 120
          periodSeconds: 30 